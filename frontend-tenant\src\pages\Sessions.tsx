import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Search, 
  MoreHorizontal, 
  Eye, 
  MessageSquare,
  Calendar,
  Clock,
  User,
  Bot,
  Filter
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON>rollArea } from "@/components/ui/scroll-area";

// 模拟数据
const sessions = [
  {
    id: "1",
    clientId: "用户001",
    application: "客服机器人",
    agent: "通用客服助手",
    status: "进行中",
    messageCount: 12,
    duration: "5分钟",
    createdAt: "2024-01-20 14:30:00",
    updatedAt: "2024-01-20 14:35:00",
    userAgent: "Chrome/120.0.0.0",
    ipAddress: "*************"
  },
  {
    id: "2",
    clientId: "用户002", 
    application: "销售助手",
    agent: "销售顾问",
    status: "已结束",
    messageCount: 8,
    duration: "3分钟",
    createdAt: "2024-01-20 14:25:00",
    updatedAt: "2024-01-20 14:28:00",
    userAgent: "Safari/17.0",
    ipAddress: "*************"
  },
  {
    id: "3",
    clientId: "用户003",
    application: "技术支持", 
    agent: "技术诊断专家",
    status: "进行中",
    messageCount: 15,
    duration: "8分钟",
    createdAt: "2024-01-20 14:20:00",
    updatedAt: "2024-01-20 14:28:00",
    userAgent: "Firefox/121.0",
    ipAddress: "*************"
  },
  {
    id: "4",
    clientId: "用户004",
    application: "客服机器人",
    agent: "通用客服助手", 
    status: "已结束",
    messageCount: 6,
    duration: "2分钟",
    createdAt: "2024-01-20 14:15:00",
    updatedAt: "2024-01-20 14:17:00",
    userAgent: "Chrome/120.0.0.0",
    ipAddress: "*************"
  },
];

const sampleMessages = [
  { category: "User", content: "你好，我想咨询一下产品价格", timestamp: "14:30:15" },
  { category: "Assistant", content: "您好！很高兴为您服务。请问您想了解哪款产品的价格呢？", timestamp: "14:30:18" },
  { category: "User", content: "我想了解企业版的价格", timestamp: "14:30:45" },
  { category: "Assistant", content: "企业版目前的价格是每月999元，包含以下功能：\n- 无限制API调用\n- 专属客服支持\n- 定制化配置\n- 数据备份服务", timestamp: "14:30:50" },
  { category: "User", content: "有没有优惠活动？", timestamp: "14:31:20" },
  { category: "Assistant", content: "目前我们有新用户首月8折优惠活动，首月只需799元。您可以先试用，满意后再续费。需要我为您安排试用吗？", timestamp: "14:31:25" },
];

export default function Sessions() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedApp, setSelectedApp] = useState("");
  const [selectedSession, setSelectedSession] = useState<typeof sessions[0] | null>(null);

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = session.clientId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.application.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.agent.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !selectedStatus || selectedStatus === "all" || session.status === selectedStatus;
    const matchesApp = !selectedApp || selectedApp === "all" || session.application === selectedApp;
    
    return matchesSearch && matchesStatus && matchesApp;
  });

  const applications = ["客服机器人", "销售助手", "技术支持", "产品咨询"];
  const statuses = ["进行中", "已结束"];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">会话管理</h1>
          <p className="text-muted-foreground mt-1">监控和管理用户与AI的对话会话</p>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总会话数
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{sessions.length}</div>
            <p className="text-xs text-muted-foreground">
              今日新增
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              进行中
            </CardTitle>
            <Clock className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {sessions.filter(s => s.status === "进行中").length}
            </div>
            <p className="text-xs text-muted-foreground">
              实时对话
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              平均时长
            </CardTitle>
            <Clock className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">4.5分钟</div>
            <p className="text-xs text-muted-foreground">
              平均会话时长
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总消息数
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {sessions.reduce((sum, s) => sum + s.messageCount, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              累计消息
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索用户ID、应用或智能体..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted border-border"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-32 bg-muted border-border">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="all">全部状态</SelectItem>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedApp} onValueChange={setSelectedApp}>
              <SelectTrigger className="w-40 bg-muted border-border">
                <SelectValue placeholder="应用" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="all">全部应用</SelectItem>
                {applications.map((app) => (
                  <SelectItem key={app} value={app}>{app}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="text-muted-foreground">用户信息</TableHead>
                <TableHead className="text-muted-foreground">应用/智能体</TableHead>
                <TableHead className="text-muted-foreground">状态</TableHead>
                <TableHead className="text-muted-foreground">消息数</TableHead>
                <TableHead className="text-muted-foreground">时长</TableHead>
                <TableHead className="text-muted-foreground">开始时间</TableHead>
                <TableHead className="text-muted-foreground text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSessions.map((session) => (
                <TableRow key={session.id} className="border-border hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                        <User className="w-4 h-4 text-primary-foreground" />
                      </div>
                      <div>
                        <div className="font-semibold text-foreground">{session.clientId}</div>
                        <div className="text-xs text-muted-foreground">{session.ipAddress}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium text-foreground">{session.application}</div>
                      <div className="text-sm text-muted-foreground flex items-center">
                        <Bot className="w-3 h-3 mr-1" />
                        {session.agent}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={session.status === "进行中" ? "default" : "secondary"}
                      className={session.status === "进行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                    >
                      {session.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-foreground">{session.messageCount} 条</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-foreground">{session.duration}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-muted-foreground">
                      <Calendar className="mr-1 h-3 w-3" />
                      {session.createdAt.split(' ')[1]}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-popover border-border">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => setSelectedSession(session)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 会话详情对话框 */}
      <Dialog open={!!selectedSession} onOpenChange={() => setSelectedSession(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-foreground">会话详情</DialogTitle>
            <DialogDescription>
              查看完整的对话记录和会话信息
            </DialogDescription>
          </DialogHeader>
          {selectedSession && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 会话信息 */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-foreground">会话信息</h4>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">用户ID:</span>
                      <span className="text-foreground">{selectedSession.clientId}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">应用:</span>
                      <span className="text-foreground">{selectedSession.application}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">智能体:</span>
                      <span className="text-foreground">{selectedSession.agent}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">状态:</span>
                      <Badge 
                        variant={selectedSession.status === "进行中" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {selectedSession.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">消息数:</span>
                      <span className="text-foreground">{selectedSession.messageCount}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">时长:</span>
                      <span className="text-foreground">{selectedSession.duration}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">IP地址:</span>
                      <span className="text-foreground">{selectedSession.ipAddress}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">浏览器:</span>
                      <span className="text-foreground">{selectedSession.userAgent}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 对话记录 */}
              <div className="lg:col-span-2">
                <h4 className="font-medium text-foreground mb-4">对话记录</h4>
                <ScrollArea className="h-96 bg-muted/30 rounded-lg p-4">
                  <div className="space-y-4">
                    {sampleMessages.map((message, index) => (
                      <div 
                        key={index} 
                        className={`flex ${message.category === 'User' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div 
                          className={`max-w-[80%] rounded-lg p-3 ${
                            message.category === 'User' 
                              ? 'bg-primary text-primary-foreground' 
                              : 'bg-card border border-border'
                          }`}
                        >
                          <div className="text-sm whitespace-pre-wrap">
                            {message.content}
                          </div>
                          <div className={`text-xs mt-1 ${
                            message.category === 'User' 
                              ? 'text-primary-foreground/70' 
                              : 'text-muted-foreground'
                          }`}>
                            {message.timestamp}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

    </div>
  );
}