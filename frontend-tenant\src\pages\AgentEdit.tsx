import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  Save, 
  Trash2, 
  Bot,
  AlertTriangle,
  Database,
  Key,
  Settings,
  Zap,
  TestTube,
  Archive,
  RefreshCw
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

// 模拟数据 - 实际应用中从API获取
const agentData = {
  "1": {
    id: "1",
    name: "通用客服助手",
    description: "处理常见客户咨询和问题解答",
    key: "general-support",
    model: "gpt-4",
    application: "客服机器人",
    applicationId: "1",
    status: "运行中",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
    isArchived: false,
    sessionCount: 856,
    extra: {
      dbType: "MySql",
      dbConnectionString: "Server=localhost;Database=customer_db;Uid=user;Pwd=****;",
      cachedSchema: "CREATE TABLE customers (id INT, name VARCHAR(100), email VARCHAR(100));",
      cachedAt: "2024-01-20T10:30:00Z"
    },
    settings: {
      maxTokens: 2048,
      temperature: 0.7,
      topP: 0.9,
      presencePenalty: 0.0,
      frequencyPenalty: 0.0,
      enableStreaming: true,
      enableFunctionCalling: true,
      systemPrompt: "你是一个专业的客服助手，请礼貌地回答用户的问题。"
    },
    performance: {
      avgResponseTime: "1.2秒",
      successRate: 96.5,
      totalRequests: 5420,
      errorCount: 12
    }
  }
};

const applications = ["客服机器人", "销售助手", "技术支持", "产品咨询"];
const models = ["gpt-4", "gpt-3.5-turbo", "claude-3", "gemini-pro"];
const dbTypes = ["MySql", "PostgreSql", "Sqlite"];

// 智能体模板数据
const agentTemplates = [
  {
    id: "customer-service",
    name: "智能客服模板",
    description: "专业的客户服务智能体，擅长处理常见问题、投诉处理和服务咨询",
    category: "客户服务",
    features: ["多轮对话", "情感分析", "问题分类", "知识库检索"],
    systemPrompt: "你是一个专业的客服代表，请始终保持礼貌、耐心和专业的态度。你的任务是帮助客户解决问题，提供准确的信息，并确保客户满意度。如果遇到无法解决的问题，请及时转接人工客服。",
    settings: {
      temperature: 0.3,
      maxTokens: 2048,
      enableKnowledgeBase: true,
      enableEmotionAnalysis: true,
      responseStyle: "专业礼貌"
    },
    useCases: ["客户咨询", "投诉处理", "产品介绍", "售后服务"],
    icon: "👥"
  },
  {
    id: "sales-assistant",
    name: "销售助手模板",
    description: "专门用于销售场景的智能体，能够进行产品推荐、价格咨询和购买引导",
    category: "销售营销",
    features: ["产品推荐", "价格咨询", "购买引导", "客户画像"],
    systemPrompt: "你是一个专业的销售顾问，具有丰富的产品知识和销售经验。你的目标是通过友好的交流了解客户需求，提供合适的产品推荐，并引导客户完成购买决策。请始终以客户利益为重，提供诚实可信的建议。",
    settings: {
      temperature: 0.7,
      maxTokens: 2048,
      enableProductRecommendation: true,
      enableCustomerProfiling: true,
      responseStyle: "友好专业"
    },
    useCases: ["产品推荐", "价格咨询", "购买引导", "促销活动"],
    icon: "💼"
  },
  {
    id: "technical-support",
    name: "技术支持模板",
    description: "专业的技术支持智能体，能够诊断技术问题并提供解决方案",
    category: "技术支持",
    features: ["问题诊断", "解决方案", "步骤指导", "知识库"],
    systemPrompt: "你是一个专业的技术支持工程师，具有丰富的技术知识和故障排除经验。请用清晰易懂的语言解释技术问题，提供详细的解决步骤，并确保用户能够理解和执行。如果问题复杂，请及时升级到高级技术支持。",
    settings: {
      temperature: 0.2,
      maxTokens: 3072,
      enableTechnicalDiagnosis: true,
      enableStepByStepGuide: true,
      responseStyle: "专业详细"
    },
    useCases: ["故障诊断", "技术咨询", "操作指导", "产品使用"],
    icon: "🔧"
  },
  {
    id: "content-creator",
    name: "内容创作模板",
    description: "专门用于内容创作的智能体，能够生成各种类型的文本内容",
    category: "内容创作",
    features: ["文章写作", "创意策划", "文案撰写", "内容优化"],
    systemPrompt: "你是一个经验丰富的内容创作专家，具有出色的写作能力和创意思维。你能够根据不同的需求创作高质量的内容，包括文章、广告文案、社交媒体内容等。请确保内容原创、吸引人且符合目标受众的需求。",
    settings: {
      temperature: 0.8,
      maxTokens: 4096,
      enableCreativeWriting: true,
      enableContentOptimization: true,
      responseStyle: "创意生动"
    },
    useCases: ["文章撰写", "广告文案", "社媒内容", "创意策划"],
    icon: "✍️"
  },
  {
    id: "data-analyst",
    name: "数据分析模板",
    description: "专业的数据分析智能体，能够处理数据查询和分析任务",
    category: "数据分析",
    features: ["数据查询", "统计分析", "报表生成", "趋势预测"],
    systemPrompt: "你是一个专业的数据分析师，具有强大的数据处理和分析能力。你能够理解用户的数据需求，执行精确的查询，并提供清晰的分析结果和洞察。请确保数据的准确性，并用易懂的方式解释分析结果。",
    settings: {
      temperature: 0.1,
      maxTokens: 2048,
      enableDataQuery: true,
      enableStatisticalAnalysis: true,
      responseStyle: "准确简洁"
    },
    useCases: ["数据查询", "报表生成", "趋势分析", "业务洞察"],
    icon: "📊"
  }
];

export default function AgentEdit() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const agent = agentData[id as keyof typeof agentData];
  
  const [formData, setFormData] = useState({
    name: agent?.name || "",
    description: agent?.description || "",
    key: agent?.key || "",
    model: agent?.model || "",
    application: agent?.application || "",
    systemPrompt: agent?.settings?.systemPrompt || "",
    selectedTemplate: "", // 新增：选中的模板ID
    settings: {
      maxTokens: agent?.settings?.maxTokens || 2048,
      temperature: agent?.settings?.temperature || 0.7,
      topP: agent?.settings?.topP || 0.9,
      presencePenalty: agent?.settings?.presencePenalty || 0.0,
      frequencyPenalty: agent?.settings?.frequencyPenalty || 0.0,
      enableStreaming: agent?.settings?.enableStreaming !== undefined ? agent.settings.enableStreaming : true,
      enableFunctionCalling: agent?.settings?.enableFunctionCalling !== undefined ? agent.settings.enableFunctionCalling : true
    },
    database: {
      dbType: agent?.extra?.dbType || "MySql",
      host: "",
      port: "",
      database: "",
      username: "",
      password: "",
      cachedSchema: agent?.extra?.cachedSchema || ""
    }
  });

  const [activeTab, setActiveTab] = useState("basic");
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  
  // 获取选中的模板
  const selectedTemplate = agentTemplates.find(t => t.id === formData.selectedTemplate);

  // 应用模板
  const applyTemplate = (templateId: string) => {
    const template = agentTemplates.find(t => t.id === templateId);
    if (template) {
      setFormData({
        ...formData,
        selectedTemplate: templateId,
        systemPrompt: template.systemPrompt,
        settings: {
          ...formData.settings,
          temperature: template.settings.temperature,
          maxTokens: template.settings.maxTokens
        }
      });
    }
  };

  if (!agent) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">智能体不存在</h2>
          <p className="text-muted-foreground mb-4">未找到指定的智能体信息</p>
          <Link to="/agents">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回智能体列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const handleSave = () => {
    toast({
      title: "保存成功",
      description: "智能体配置已更新",
    });
    navigate("/agents");
  };

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    // 模拟测试数据库连接
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsTestingConnection(false);
    toast({
      title: "连接测试成功",
      description: "数据库连接正常，架构已缓存",
    });
  };

  const handleArchive = () => {
    toast({
      title: "归档成功",
      description: "智能体已被归档",
      variant: "default"
    });
    navigate("/agents");
  };

  const handleDelete = () => {
    toast({
      title: "删除成功",
      description: "智能体已被删除",
      variant: "destructive"
    });
    navigate("/agents");
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/agents">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Bot className="w-5 h-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-foreground">编辑智能体</h1>
              <p className="text-muted-foreground mt-1">配置智能体的参数和数据库连接</p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge 
            variant={agent.status === "运行中" ? "default" : "secondary"}
            className={agent.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
          >
            {agent.status}
          </Badge>
          <Button onClick={handleSave} className="bg-gradient-primary hover:opacity-90">
            <Save className="mr-2 h-4 w-4" />
            保存配置
          </Button>
        </div>
      </div>

      {/* 性能概览卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总请求数
            </CardTitle>
            <Zap className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{agent.performance.totalRequests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              累计处理
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              成功率
            </CardTitle>
            <TestTube className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{agent.performance.successRate}%</div>
            <p className="text-xs text-muted-foreground">
              响应成功率
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              响应时间
            </CardTitle>
            <RefreshCw className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{agent.performance.avgResponseTime}</div>
            <p className="text-xs text-muted-foreground">
              平均响应
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              错误数量
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{agent.performance.errorCount}</div>
            <p className="text-xs text-muted-foreground">
              处理错误
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 标签页导航 */}
      <div className="flex space-x-1">
        <Button
          variant={activeTab === "basic" ? "default" : "ghost"}
          onClick={() => setActiveTab("basic")}
          className="h-9"
        >
          基本配置
        </Button>
        <Button
          variant={activeTab === "template" ? "default" : "ghost"}
          onClick={() => setActiveTab("template")}
          className="h-9"
        >
          智能体模板
        </Button>
        <Button
          variant={activeTab === "database" ? "default" : "ghost"}
          onClick={() => setActiveTab("database")}
          className="h-9"
        >
          数据库配置
        </Button>
        <Button
          variant={activeTab === "advanced" ? "default" : "ghost"}
          onClick={() => setActiveTab("advanced")}
          className="h-9"
        >
          高级设置
        </Button>
        <Button
          variant={activeTab === "danger" ? "default" : "ghost"}
          onClick={() => setActiveTab("danger")}
          className="h-9 text-destructive"
        >
          危险操作
        </Button>
      </div>

      {/* 基本配置 */}
      {activeTab === "basic" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">基本信息</CardTitle>
            <CardDescription>智能体的基本配置信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="agent-name">智能体名称</Label>
                <Input
                  id="agent-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="bg-muted border-border"
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="agent-description">描述</Label>
              <Textarea
                id="agent-description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="bg-muted border-border min-h-[100px]"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="system-prompt">系统提示词</Label>
              <Textarea
                id="system-prompt"
                value={formData.systemPrompt}
                onChange={(e) => setFormData({...formData, systemPrompt: e.target.value})}
                placeholder="输入系统提示词来定义智能体的行为和角色..."
                className="bg-muted border-border min-h-[120px]"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* 智能体模板 */}
      {activeTab === "template" && (
        <div className="space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">选择智能体模板</CardTitle>
              <CardDescription>
                从预定义的模板中选择，模板包含了针对特定场景优化的提示词和参数配置
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {agentTemplates.map((template) => (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                      formData.selectedTemplate === template.id
                        ? "ring-2 ring-primary bg-primary/5"
                        : "hover:bg-muted/50"
                    }`}
                    onClick={() => applyTemplate(template.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">{template.icon}</div>
                        <div>
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          <Badge variant="outline" className="text-xs">
                            {template.category}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-muted-foreground mb-3">
                        {template.description}
                      </p>
                      <div className="space-y-2">
                        <div className="flex flex-wrap gap-1">
                          {template.features.slice(0, 3).map((feature) => (
                            <Badge key={feature} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                          {template.features.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{template.features.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 选中模板的详细信息 */}
          {selectedTemplate && (
            <Card className="bg-card border-border shadow-card animate-fade-in">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">{selectedTemplate.icon}</div>
                  <div>
                    <CardTitle className="text-foreground">{selectedTemplate.name}</CardTitle>
                    <CardDescription>{selectedTemplate.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-foreground mb-2">核心功能</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedTemplate.features.map((feature) => (
                          <Badge key={feature} variant="secondary">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-foreground mb-2">适用场景</h4>
                      <div className="space-y-1">
                        {selectedTemplate.useCases.map((useCase) => (
                          <div key={useCase} className="flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                            <span className="text-sm text-muted-foreground">{useCase}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-foreground mb-2">预设参数</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">Temperature</span>
                          <Badge variant="outline">{selectedTemplate.settings.temperature}</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">Max Tokens</span>
                          <Badge variant="outline">{selectedTemplate.settings.maxTokens}</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">响应风格</span>
                          <Badge variant="outline">{selectedTemplate.settings.responseStyle}</Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-foreground mb-2">系统提示词预览</h4>
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm text-foreground leading-relaxed">
                      {selectedTemplate.systemPrompt}
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-between pt-4 border-t border-border">
                  <p className="text-sm text-muted-foreground">
                    选择此模板将自动应用其系统提示词和参数配置
                  </p>
                  <Button 
                    onClick={() => applyTemplate(selectedTemplate.id)}
                    className="bg-gradient-primary hover:opacity-90"
                  >
                    应用模板
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* 数据库配置 */}
      {activeTab === "database" && (
        <div className="space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">数据库连接</CardTitle>
              </div>
              <CardDescription>配置智能体的数据库访问权限</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6">
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="db-type">数据库类型</Label>
                    <Select 
                      value={formData.database.dbType} 
                      onValueChange={(value) => setFormData({
                        ...formData, 
                        database: {...formData.database, dbType: value}
                      })}
                    >
                      <SelectTrigger className="bg-muted border-border">
                        <SelectValue placeholder="选择数据库类型" />
                      </SelectTrigger>
                      <SelectContent className="bg-popover border-border">
                        {dbTypes.map((type) => (
                          <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="db-host">主机地址</Label>
                    <Input
                      id="db-host"
                      value={formData.database.host}
                      onChange={(e) => setFormData({
                        ...formData, 
                        database: {...formData.database, host: e.target.value}
                      })}
                      placeholder="localhost"
                      className="bg-muted border-border"
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="db-port">端口</Label>
                    <Input
                      id="db-port"
                      value={formData.database.port}
                      onChange={(e) => setFormData({
                        ...formData, 
                        database: {...formData.database, port: e.target.value}
                      })}
                      placeholder="3306"
                      className="bg-muted border-border"
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="db-name">数据库名称</Label>
                    <Input
                      id="db-name"
                      value={formData.database.database}
                      onChange={(e) => setFormData({
                        ...formData, 
                        database: {...formData.database, database: e.target.value}
                      })}
                      placeholder="mydb"
                      className="bg-muted border-border"
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="db-username">用户名</Label>
                    <Input
                      id="db-username"
                      value={formData.database.username}
                      onChange={(e) => setFormData({
                        ...formData, 
                        database: {...formData.database, username: e.target.value}
                      })}
                      placeholder="username"
                      className="bg-muted border-border"
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="db-password">密码</Label>
                  <Input
                    id="db-password"
                    type="password"
                    value={formData.database.password}
                    onChange={(e) => setFormData({
                      ...formData, 
                      database: {...formData.database, password: e.target.value}
                    })}
                    placeholder="password"
                    className="bg-muted border-border"
                  />
                  <p className="text-xs text-muted-foreground">
                    密码将被安全加密存储
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={handleTestConnection}
                    disabled={isTestingConnection}
                    className="w-fit"
                  >
                    {isTestingConnection ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Key className="mr-2 h-4 w-4" />
                    )}
                    {isTestingConnection ? "测试中..." : "测试连接"}
                  </Button>
                  <Badge variant="outline" className="text-green-500 border-green-500/30">
                    连接正常
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">数据库架构</CardTitle>
              <CardDescription>缓存的数据库表结构信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="cached-schema">缓存架构</Label>
                <Textarea
                  id="cached-schema"
                  value={formData.database.cachedSchema}
                  onChange={(e) => setFormData({
                    ...formData, 
                    database: {...formData.database, cachedSchema: e.target.value}
                  })}
                  className="bg-muted border-border min-h-[200px] font-mono text-sm"
                  readOnly
                />
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">
                    最后更新：{new Date(agent.extra.cachedAt).toLocaleString()}
                  </p>
                  <Button variant="outline" size="sm">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    刷新架构
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 高级设置 */}
      {activeTab === "advanced" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">高级设置</CardTitle>
            <CardDescription>其他配置选项和实验性功能</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="agent-id">智能体ID</Label>
                <Input
                  id="agent-id"
                  value={agent.id}
                  disabled
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  智能体的唯一标识符，创建后不可修改
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="created-at">创建时间</Label>
                <Input
                  id="created-at"
                  value={agent.createdAt}
                  disabled
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="updated-at">最后更新</Label>
                <Input
                  id="updated-at"
                  value={agent.updatedAt}
                  disabled
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="session-count">会话数量</Label>
                <Input
                  id="session-count"
                  value={agent.sessionCount.toLocaleString()}
                  disabled
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  该智能体处理的总会话数量
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 危险操作 */}
      {activeTab === "danger" && (
        <Card className="bg-card border-border shadow-card border-destructive/50">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <CardTitle className="text-foreground">危险操作</CardTitle>
            </div>
            <CardDescription>
              这些操作将对智能体产生重要影响，请谨慎操作
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                <h4 className="font-medium text-foreground mb-2">归档智能体</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  归档智能体将停止其服务，但保留所有配置和数据。归档后可以恢复。
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" className="border-orange-500/30 text-orange-500 hover:bg-orange-500/10">
                      <Archive className="mr-2 h-4 w-4" />
                      归档智能体
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-card border-border">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-foreground">确认归档智能体</AlertDialogTitle>
                      <AlertDialogDescription>
                        您即将归档智能体 "{agent.name}"。归档后：
                        <ul className="list-disc list-inside mt-2 space-y-1">
                          <li>智能体将停止服务</li>
                          <li>现有会话将被终止</li>
                          <li>配置和数据将被保留</li>
                          <li>可以在后续恢复使用</li>
                        </ul>
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction onClick={handleArchive} className="bg-orange-500 hover:bg-orange-500/90">
                        确认归档
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>

              <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <h4 className="font-medium text-foreground mb-2">删除智能体</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  删除智能体将永久移除所有相关的配置、会话记录和数据。此操作不可撤销。
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive">
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除智能体
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-card border-border">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-foreground">确认删除智能体</AlertDialogTitle>
                      <AlertDialogDescription>
                        您即将永久删除智能体 "{agent.name}"。此操作将：
                        <ul className="list-disc list-inside mt-2 space-y-1">
                          <li>删除所有配置信息</li>
                          <li>删除所有会话记录</li>
                          <li>删除数据库连接配置</li>
                          <li>终止所有相关服务</li>
                        </ul>
                        <br />
                        <strong>此操作不可撤销，请确认是否继续？</strong>
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/90">
                        确认删除
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}