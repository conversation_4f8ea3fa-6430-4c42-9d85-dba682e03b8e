import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import {
  Eye,
  EyeOff,
  RefreshCw,
  User,
  Lock,
  Shield,
  Building2,
  AlertCircle,
  CheckCircle
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import AuthAPI from "@/api/AuthAPI";
import { log } from "console";

export default function Login() {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    captcha: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [captchaKey, setCaptchaKey] = useState("");
  const [captchaImage, setCaptchaImage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { toast } = useToast();
  const navigate = useNavigate();


  // 获取验证码
  const generateCaptcha = async () => {
    try {
      const response = await AuthAPI.getCaptcha();
      setCaptchaKey(response.captchaKey);
      setCaptchaImage(response.captchaBase64);
      setFormData(prev => ({ ...prev, captcha: "" }));
      if (errors.captcha) setErrors(prev => ({ ...prev, captcha: "" }));
    } catch (error) {
      toast({
        title: "获取验证码失败",
        description: "请刷新页面重试",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    generateCaptcha();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = "请输入用户名或邮箱";
    } else if (formData.username.includes("@")) {
      // 简单邮箱验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.username)) {
        newErrors.username = "请输入有效的邮箱地址";
      }
    } else if (formData.username.length < 3) {
      newErrors.username = "用户名至少3个字符";
    }

    if (!formData.password) {
      newErrors.password = "请输入密码";
    } else if (formData.password.length < 6) {
      newErrors.password = "密码至少6位";
    }

    if (!formData.captcha) {
      newErrors.captcha = "请输入验证码";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const loginData = {
        username: formData.username,
        password: formData.password,
        captcha: formData.captcha,
        captchaKey: captchaKey, // 添加验证码key
      };

      const result = await AuthAPI.login(loginData);

      toast({
        title: "登录成功",
        description: "欢迎回来！正在跳转到仪表板...",
        duration: 3000,
      });

      navigate("/");

    } catch (error: any) {
      toast({
        title: "登录失败",
        description: error?.response?.data?.message || error?.message || "登录失败，请重试",
        variant: "destructive",
      });
      generateCaptcha(); // 重新生成验证码
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/30 to-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-card border-border shadow-elegant">
        <CardHeader className="space-y-1 pb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center">
              <Building2 className="w-6 h-6 text-primary-foreground" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center text-foreground">
            Aihub 租户管理
          </CardTitle>
          <CardDescription className="text-center text-muted-foreground">
            请登录您的管理账户
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 用户名输入 */}
            <div className="space-y-2">
              <Label htmlFor="username" className="text-foreground">
                用户名 / 邮箱
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  id="username"
                  type="text"
                  placeholder="输入用户名或邮箱"
                  value={formData.username}
                  onChange={(e) => handleInputChange("username", e.target.value)}
                  className={cn(
                    "pl-10",
                    errors.username && "border-destructive focus-visible:ring-destructive"
                  )}
                  disabled={isLoading}
                />
              </div>
              {errors.username && (
                <div className="flex items-center space-x-1 text-destructive text-xs">
                  <AlertCircle className="h-3 w-3" />
                  <span>{errors.username}</span>
                </div>
              )}
            </div>

            {/* 密码输入 */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-foreground">
                密码
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="输入密码"
                  value={formData.password}
                  onChange={(e) => handleInputChange("password", e.target.value)}
                  className={cn(
                    "pl-10 pr-10",
                    errors.password && "border-destructive focus-visible:ring-destructive"
                  )}
                  disabled={isLoading}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <div className="flex items-center space-x-1 text-destructive text-xs">
                  <AlertCircle className="h-3 w-3" />
                  <span>{errors.password}</span>
                </div>
              )}
            </div>

            {/* 验证码输入 */}
            <div className="space-y-2">
              <Label htmlFor="captcha" className="text-foreground">
                验证码
              </Label>
              <div className="flex space-x-2">
                <div className="relative flex-1">
                  <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    id="captcha"
                    type="text"
                    placeholder="输入验证码"
                    value={formData.captcha}
                    onChange={(e) => handleInputChange("captcha", e.target.value.slice(0, 4))}
                    className={cn(
                      "pl-10",
                      errors.captcha && "border-destructive focus-visible:ring-destructive"
                    )}
                    disabled={isLoading}
                    maxLength={4}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <img
                      src={captchaImage}
                      alt="验证码"
                      className="w-24 h-10 border border-border rounded cursor-pointer bg-muted"
                      onClick={generateCaptcha}
                    />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generateCaptcha}
                    disabled={isLoading}
                    className="h-10 w-10 p-0"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {errors.captcha && (
                <div className="flex items-center space-x-1 text-destructive text-xs">
                  <AlertCircle className="h-3 w-3" />
                  <span>{errors.captcha}</span>
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                点击验证码图片或刷新按钮重新生成
              </p>
            </div>

            <Separator />

            {/* 登录按钮 */}
            <Button
              type="submit"
              className="w-full bg-gradient-primary hover:opacity-90 h-11"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>登录中...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4" />
                  <span>登录</span>
                </div>
              )}
            </Button>
          </form>

          {/* 底部信息 */}
          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <Separator className="flex-1" />
              <Badge variant="secondary" className="text-xs">
                安全登录
              </Badge>
              <Separator className="flex-1" />
            </div>

            <Alert className="border-primary/20 bg-primary/5">
              <Shield className="h-4 w-4 text-primary" />
              <AlertDescription className="text-xs text-muted-foreground">
                为了您的账户安全，请妥善保管登录凭据。如有异常登录行为，系统将自动锁定账户。
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}