import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Activity,
  Calendar,
  Zap
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// 模拟数据
const applications = [
  {
    id: "1",
    name: "客服机器人",
    description: "智能客服系统，提供24/7在线服务",
    status: "启用",
    createdAt: "2024-01-15",
    agentCount: 3,
    sessionCount: 1234,
    lastActivity: "2分钟前"
  },
  {
    id: "2", 
    name: "销售助手",
    description: "销售流程自动化和客户跟进",
    status: "启用",
    createdAt: "2024-01-20",
    agentCount: 2,
    sessionCount: 892,
    lastActivity: "5分钟前"
  },
  {
    id: "3",
    name: "技术支持",
    description: "技术问题诊断和解决方案推荐",
    status: "禁用",
    createdAt: "2024-02-01",
    agentCount: 1,
    sessionCount: 445,
    lastActivity: "1小时前"
  },
  {
    id: "4",
    name: "产品咨询",
    description: "产品信息查询和推荐服务",
    status: "启用",
    createdAt: "2024-02-10",
    agentCount: 2,
    sessionCount: 678,
    lastActivity: "刚刚"
  },
];

export default function Applications() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newAppData, setNewAppData] = useState({
    name: "",
    description: ""
  });

  const filteredApplications = applications.filter(app =>
    app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">应用管理</h1>
          <p className="text-muted-foreground mt-1">管理您的AI应用和服务</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Plus className="mr-2 h-4 w-4" />
              创建应用
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px] bg-card border-border">
            <DialogHeader>
              <DialogTitle className="text-foreground">创建新应用</DialogTitle>
              <DialogDescription>
                创建一个新的AI应用来管理您的智能体和服务
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name" className="text-foreground">应用名称</Label>
                <Input
                  id="name"
                  value={newAppData.name}
                  onChange={(e) => setNewAppData({...newAppData, name: e.target.value})}
                  placeholder="输入应用名称..."
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description" className="text-foreground">应用描述</Label>
                <Textarea
                  id="description"
                  value={newAppData.description}
                  onChange={(e) => setNewAppData({...newAppData, description: e.target.value})}
                  placeholder="描述应用的用途和功能..."
                  className="bg-muted border-border"
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="submit" 
                className="bg-gradient-primary hover:opacity-90"
                onClick={() => {
                  // 这里实际应用中会调用API创建应用
                  // 模拟创建成功，跳转到编辑页面
                  const newAppId = "new-" + Date.now(); // 模拟新应用ID
                  setIsCreateDialogOpen(false);
                  setNewAppData({ name: "", description: "" });
                  // 跳转到编辑页面
                  window.location.href = `/applications/${newAppId}/edit`;
                }}
                disabled={!newAppData.name.trim()}
              >
                创建应用
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总应用数
            </CardTitle>
            <Zap className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{applications.length}</div>
            <p className="text-xs text-muted-foreground">
              +2 较上月
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              启用中
            </CardTitle>
            <Activity className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {applications.filter(app => app.status === "启用").length}
            </div>
            <p className="text-xs text-muted-foreground">
              正常运行
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总智能体
            </CardTitle>
            <Activity className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {applications.reduce((sum, app) => sum + app.agentCount, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              分布在各应用
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总会话数
            </CardTitle>
            <Activity className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {applications.reduce((sum, app) => sum + app.sessionCount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              累计对话
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索应用名称或描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted border-border"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="text-muted-foreground">应用名称</TableHead>
                <TableHead className="text-muted-foreground">状态</TableHead>
                <TableHead className="text-muted-foreground">智能体</TableHead>
                <TableHead className="text-muted-foreground">会话数</TableHead>
                <TableHead className="text-muted-foreground">创建时间</TableHead>
                <TableHead className="text-muted-foreground">最后活动</TableHead>
                <TableHead className="text-muted-foreground text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredApplications.map((app) => (
                <TableRow key={app.id} className="border-border hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold text-foreground">{app.name}</div>
                      <div className="text-sm text-muted-foreground">{app.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={app.status === "启用" ? "default" : "secondary"}
                      className={app.status === "启用" ? "bg-green-500/20 text-green-500 border-green-500/30" : "bg-red-500/20 text-red-500 border-red-500/30"}
                    >
                      {app.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-foreground">{app.agentCount} 个</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-foreground">{app.sessionCount.toLocaleString()}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-muted-foreground">
                      <Calendar className="mr-1 h-3 w-3" />
                      {app.createdAt}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-muted-foreground">{app.lastActivity}</span>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-popover border-border">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <Link to={`/applications/${app.id}/edit`}>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            编辑
                          </DropdownMenuItem>
                        </Link>
                        <Link to={`/applications/${app.id}`}>
                          <DropdownMenuItem>
                            <Activity className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                        </Link>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}