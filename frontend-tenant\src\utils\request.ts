import axios, { type InternalAxiosRequestConfig, type AxiosResponse, AxiosHeaders } from "axios";
import { Auth } from "./auth";

// 响应结果枚举
enum ResultEnum {
  SUCCESS = 200,
  BIZ_SUCCESS = "00000",
  ACCESS_TOKEN_INVALID = "A0230",
  REFRESH_TOKEN_INVALID = "A0231",
}

import { toast as sonnerToast } from "@/components/ui/sonner";

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_API_URL,
  timeout: 50000,
  headers: { "Content-Type": "application/json;charset=utf-8" },
});

// 是否正在刷新 token
let isRefreshing = false;
const waitingQueue: Array<() => void> = [];

/**
 * 泛型 request 方法
 */
async function request<T = any>(config: Partial<InternalAxiosRequestConfig & { headers?: AxiosHeaders | Record<string, string> }>): Promise<T> {
  if (config.headers && !(config.headers instanceof AxiosHeaders)) {
    config.headers = new AxiosHeaders(config.headers);
  }else if (!config.headers) {
    config.headers = new AxiosHeaders();
  }

  // 自动识别 FormData 并设置 Content-Type
  if (config.data instanceof FormData) {
    if (!config.headers.has("Content-Type")) {
      config.headers.set("Content-Type", "multipart/form-data");
    }
  } else {
    // 默认 JSON
    if (!config.headers.has("Content-Type")) {
      config.headers.set("Content-Type", "application/json;charset=utf-8");
    }
  }

  return service(config);
}

/**
 * 请求拦截器
 * 自动添加 Authorization
 */
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const accessToken = Auth.getAccessToken();
    if (config.headers.Authorization !== "no-auth" && accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    } else {
      delete config.headers.Authorization;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

/**
 * 响应拦截器
 */
service.interceptors.response.use(
  (response: AxiosResponse) => {
    if (response.config.responseType === "blob") return response;

    const { code, data, msg } = response.data;
    const codeStr = String(code);

    if (codeStr === String(ResultEnum.SUCCESS) || codeStr === String(ResultEnum.BIZ_SUCCESS)) {
      return data; 
    }

    showErrorMessage(msg || "系统出错");
    return Promise.reject({ response: { data: { message: msg } } });
  },
  async (error) => {
    const { config, response } = error;

    if (!response) {
      if (error.code === "ECONNABORTED") {
        showErrorMessage("请求超时，请稍后重试");
      } else if (typeof navigator !== "undefined" && !navigator.onLine) {
        showErrorMessage("网络已断开，请检查网络连接");
      } else {
        showErrorMessage(error?.message || "请求失败，请稍后重试");
      }
      return Promise.reject(error);
    }

    const { code, msg } = response.data || {};
    const codeStr = String(code);

    if (!code && response.status === 401) {
      await handleSessionExpired();
      return Promise.reject(error);
    }

    if (codeStr === String(ResultEnum.ACCESS_TOKEN_INVALID)) {
      return handleTokenRefresh(config);
    } else if (codeStr === String(ResultEnum.REFRESH_TOKEN_INVALID)) {
      await handleSessionExpired();
      return Promise.reject({ response: { data: { message: msg } } });
    } else {
      showErrorMessage(msg || "系统出错");
      return Promise.reject({ response: { data: { message: msg } } });
    }
  }
);

/**
 * 刷新 Token 处理
 */
async function handleTokenRefresh(config: InternalAxiosRequestConfig) {
  return new Promise((resolve) => {
    const retryRequest = () => {
      config.headers.Authorization = `Bearer ${Auth.getAccessToken()}`;
      resolve(request(config));
    };

    waitingQueue.push(retryRequest);

    if (!isRefreshing) {
      isRefreshing = true;
      refreshToken()
        .then(() => {
          waitingQueue.forEach((callback) => callback());
          waitingQueue.length = 0;
        })
        .catch(async (error) => {
          console.error("handleTokenRefresh error", error);
          await handleSessionExpired();
        })
        .finally(() => {
          isRefreshing = false;
        });
    }
  });
}

/**
 * 刷新 Token
 */
async function refreshToken() {
  const refreshToken = Auth.getRefreshToken();
  if (!refreshToken) throw new Error("No refresh token");

  const response = await axios.post(
    `${import.meta.env.VITE_APP_API_URL}/api/v1/auth/refresh-token`,
    null,
    {
      params: { refreshToken },
      headers: { Authorization: "no-auth" },
    }
  );

  const { accessToken, refreshToken: newRefreshToken, expiresIn } = response.data.data;
  Auth.setTokens(accessToken, newRefreshToken, expiresIn, Auth.getRememberMe());
  return response.data.data;
}

/**
 * 会话过期处理
 */
async function handleSessionExpired() {
  showErrorMessage("您的会话已过期，请重新登录");
  Auth.clearTokens();
  window.location.href = "/login";
}

/**
 * 显示错误消息
 */
function showErrorMessage(message: string) {
  try {
    sonnerToast.error(message, { duration: 3000 });
  } catch (e) {
  }
  console.error(message);
}

export default request;
