import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Bot,
  Calendar,
  Cpu,
  Database,
  MessageCircle,
  Settings,
  ChevronRight,
  Users
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

// 模拟数据
const agents = [
  {
    id: "1",
    name: "通用客服助手",
    description: "处理常见客户咨询和问题解答",
    key: "general-support",
    model: "gpt-4",
    application: "客服机器人",
    type: "conversational", // 对话式
    parentId: null,
    status: "运行中",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
    isArchived: false,
    sessionCount: 856
  },
  {
    id: "4",
    name: "数据库查询助手",
    description: "自然语言查询数据库信息",
    key: "db-query",
    model: "gpt-4",
    application: "技术支持",
    type: "task", // 任务式
    parentId: "1", // 属于通用客服助手
    status: "运行中",
    createdAt: "2024-02-03",
    updatedAt: "2024-02-08",
    isArchived: false,
    sessionCount: 145
  },
  {
    id: "2",
    name: "销售顾问",
    description: "产品推荐和销售流程指导",
    key: "sales-advisor",
    model: "gpt-3.5-turbo",
    application: "销售助手", 
    type: "conversational", // 对话式
    parentId: null,
    status: "运行中",
    createdAt: "2024-01-18",
    updatedAt: "2024-01-22",
    isArchived: false,
    sessionCount: 623
  },
  {
    id: "5",
    name: "文档处理器",
    description: "自动处理和分析文档内容",
    key: "doc-processor",
    model: "gpt-4",
    application: "数据处理",
    type: "task", // 任务式
    parentId: "2", // 属于销售顾问
    status: "运行中",
    createdAt: "2024-02-10",
    updatedAt: "2024-02-15",
    isArchived: false,
    sessionCount: 67
  },
  {
    id: "6",
    name: "邮件分类器",
    description: "自动分类和标记客户邮件",
    key: "email-classifier",
    model: "gpt-3.5-turbo",
    application: "邮件管理",
    type: "task", // 任务式
    parentId: "2", // 属于销售顾问
    status: "运行中",
    createdAt: "2024-02-12",
    updatedAt: "2024-02-18",
    isArchived: false,
    sessionCount: 89
  },
  {
    id: "3",
    name: "技术诊断专家",
    description: "技术问题诊断和解决方案",
    key: "tech-expert",
    model: "gpt-4",
    application: "技术支持",
    type: "conversational", // 对话式
    parentId: null,
    status: "维护中",
    createdAt: "2024-02-01",
    updatedAt: "2024-02-05",
    isArchived: false,
    sessionCount: 234
  },
];

const applications = ["客服机器人", "销售助手", "技术支持", "产品咨询"];
const models = ["gpt-4", "gpt-3.5-turbo", "claude-3", "gemini-pro"];

export default function Agents() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [expandedAgents, setExpandedAgents] = useState<Set<string>>(new Set());
  
  // 新建智能体表单状态
  const [createForm, setCreateForm] = useState({
    name: "",
    description: ""
  });

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.application.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  // 获取所有对话式智能体
  const conversationalAgents = filteredAgents.filter(agent => agent.type === "conversational");
  
  // 获取指定对话式智能体的任务式子智能体
  const getTaskAgents = (parentId: string) => {
    return filteredAgents.filter(agent => agent.type === "task" && agent.parentId === parentId);
  };

  // 切换折叠状态
  const toggleExpanded = (agentId: string) => {
    const newExpanded = new Set(expandedAgents);
    if (newExpanded.has(agentId)) {
      newExpanded.delete(agentId);
    } else {
      newExpanded.add(agentId);
    }
    setExpandedAgents(newExpanded);
  };

  // 创建智能体
  const handleCreateAgent = () => {
    if (!createForm.name.trim()) return;
    
    // 模拟创建智能体并获取ID
    const newAgentId = Date.now().toString(); // 实际项目中会从API返回
    
    // 重置表单
    setCreateForm({ name: "", description: "" });
    setIsCreateDialogOpen(false);
    
    // 跳转到编辑页面
    navigate(`/agents/${newAgentId}/edit`);
  };

  // 渲染智能体卡片
  const renderAgentCard = (agent: any, isChild = false) => (
    <Card key={agent.id} className={`bg-card border-border shadow-card ${isChild ? 'ml-8 border-l-4 border-l-purple-500/50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
              agent.type === "conversational" 
                ? "bg-gradient-to-br from-blue-500 to-blue-600" 
                : "bg-gradient-to-br from-purple-500 to-purple-600"
            }`}>
              {agent.type === "conversational" ? (
                <MessageCircle className="w-5 h-5 text-white" />
              ) : (
                <Settings className="w-5 h-5 text-white" />
              )}
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-foreground">{agent.name}</CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge 
                  variant={agent.type === "conversational" ? "default" : "secondary"}
                  className={agent.type === "conversational" 
                    ? "bg-blue-500 text-white border-blue-500" 
                    : "bg-purple-500 text-white border-purple-500"
                  }
                >
                  {agent.type === "conversational" ? "对话式" : "任务式"}
                </Badge>
                <span className="text-sm text-muted-foreground">{agent.key}</span>
              </div>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-popover border-border">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <Link to={`/agents/${agent.id}/edit`}>
                <DropdownMenuItem>
                  <Edit className="mr-2 h-4 w-4" />
                  编辑配置
                </DropdownMenuItem>
              </Link>
              <Link to={`/agents/${agent.id}/test`}>
                <DropdownMenuItem>
                  <Bot className="mr-2 h-4 w-4" />
                  测试对话
                </DropdownMenuItem>
              </Link>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <CardDescription className="text-muted-foreground mb-4">
          {agent.description}
        </CardDescription>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground">应用</span>
            <Badge variant="outline" className="border-primary/30 text-primary w-fit mt-1">
              {agent.application}
            </Badge>
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground">模型</span>
            <span className="text-sm font-mono text-foreground mt-1">{agent.model}</span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground">会话数</span>
            <span className="text-sm font-semibold text-foreground mt-1">{agent.sessionCount.toLocaleString()}</span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-muted-foreground">更新时间</span>
            <div className="flex items-center text-sm text-muted-foreground mt-1">
              <Calendar className="mr-1 h-3 w-3" />
              {agent.updatedAt}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">智能体管理</h1>
          <p className="text-muted-foreground mt-1">管理您的AI智能体配置和部署</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Plus className="mr-2 h-4 w-4" />
              创建智能体
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px] bg-card border-border">
            <DialogHeader>
              <DialogTitle className="text-foreground">创建新智能体</DialogTitle>
              <DialogDescription>
                先填写基本信息，创建后可进入编辑页面进行详细配置
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="agent-name">智能体名称 *</Label>
                <Input
                  id="agent-name"
                  value={createForm.name}
                  onChange={(e) => setCreateForm({...createForm, name: e.target.value})}
                  placeholder="例如：客服助手"
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="agent-description">描述</Label>
                <Textarea
                  id="agent-description"
                  value={createForm.description}
                  onChange={(e) => setCreateForm({...createForm, description: e.target.value})}
                  placeholder="描述智能体的功能和用途..."
                  className="bg-muted border-border min-h-[100px]"
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsCreateDialogOpen(false)}
              >
                取消
              </Button>
              <Button 
                onClick={handleCreateAgent}
                disabled={!createForm.name.trim()}
                className="bg-gradient-primary hover:opacity-90"
              >
                创建并配置
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总智能体
            </CardTitle>
            <Bot className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{agents.length}</div>
            <p className="text-xs text-muted-foreground">
              +3 较上月
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              运行中
            </CardTitle>
            <Cpu className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {agents.filter(agent => agent.status === "运行中").length}
            </div>
            <p className="text-xs text-muted-foreground">
              正常服务
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              数据库类型
            </CardTitle>
            <Database className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">1</div>
            <p className="text-xs text-muted-foreground">
              MySQL数据库
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总会话数
            </CardTitle>
            <Bot className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {agents.reduce((sum, agent) => sum + agent.sessionCount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              累计对话
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground">智能体列表</CardTitle>
          <div className="flex items-center space-x-4 mt-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索智能体名称、描述或应用..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted border-border"
              />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 智能体列表 */}
      <div className="space-y-4">
        {conversationalAgents.map((conversationalAgent) => {
          const taskAgents = getTaskAgents(conversationalAgent.id);
          const isExpanded = expandedAgents.has(conversationalAgent.id);
          
          return (
            <div key={conversationalAgent.id} className="space-y-3">
              {/* 对话式智能体卡片 */}
              <Collapsible>
                <div className="relative">
                  {renderAgentCard(conversationalAgent)}
                  
                  {/* 如果有子智能体，显示折叠/展开按钮 */}
                  {taskAgents.length > 0 && (
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 bg-card border border-border shadow-sm hover:bg-muted"
                        onClick={() => toggleExpanded(conversationalAgent.id)}
                      >
                        <Users className="w-4 h-4 mr-1" />
                        <span className="text-sm">{taskAgents.length} 个任务式智能体</span>
                        <ChevronRight className={`w-4 h-4 ml-1 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
                      </Button>
                    </CollapsibleTrigger>
                  )}
                </div>
                
                {/* 任务式智能体列表 */}
                <CollapsibleContent className="space-y-3 pt-6">
                  <div className="relative">
                    {/* 连接线 */}
                    <div className="absolute left-4 top-0 w-px h-full bg-gradient-to-b from-purple-500/50 to-transparent"></div>
                    
                    {taskAgents.map((taskAgent, index) => (
                      <div key={taskAgent.id} className="relative">
                        {/* 横向连接线 */}
                        <div className="absolute left-4 top-5 w-4 h-px bg-purple-500/50"></div>
                        <div className="absolute left-4 top-5 w-2 h-2 bg-purple-500 rounded-full transform -translate-x-1/2"></div>
                        
                        <div className="animate-fade-in">
                          {renderAgentCard(taskAgent, true)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          );
        })}
        
        {conversationalAgents.length === 0 && (
          <Card className="bg-card border-border shadow-card">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Bot className="w-12 h-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">暂无智能体</h3>
              <p className="text-muted-foreground text-center mb-4">
                {searchTerm ? "没有找到匹配的智能体" : "开始创建您的第一个智能体"}
              </p>
              <Button className="bg-gradient-primary hover:opacity-90">
                <Plus className="mr-2 h-4 w-4" />
                创建智能体
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}