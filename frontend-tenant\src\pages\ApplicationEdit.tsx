import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  Save, 
  Trash2, 
  Settings,
  AlertTriangle,
  Shield,
  Database,
  Key,
  Palette,
  Upload,
  MessageSquare,
  Plus,
  X,
  Bot,
  ChevronDown
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

// 模拟数据 - 实际应用中从API获取
const applicationData = {
  "1": {
    id: "1",
    name: "客服机器人",
    description: "智能客服系统，提供24/7在线服务",
    key: "customer-service-bot-2024",
    status: "运行中",
    createdAt: "2024-01-15",
    settings: {
      autoReply: true,
      maxConcurrentSessions: 100,
      sessionTimeout: 30,
      enableLogging: true,
      enableAnalytics: true,
      rateLimitEnabled: true,
      rateLimitRequests: 1000,
      rateLimitWindow: 60
    },
    security: {
      requireAuth: false,
      allowedOrigins: ["https://company.com", "https://support.company.com"],
      ipWhitelist: "",
      enableCors: true
    },
    ui: {
      icon: "",
      signOutUrl: "",
      theme: "default",
      aiAvatar: "",
      userAvatar: ""
    },
    starterPrompts: [
      {
        id: "1",
        title: "产品咨询",
        content: "我想了解你们的产品和服务"
      },
      {
        id: "2", 
        title: "技术支持",
        content: "我遇到了技术问题，需要帮助"
      },
      {
        id: "3",
        title: "售后服务",
        content: "我需要售后服务支持"
      }
    ],
    selectedAgentId: "1", // 默认选择第一个智能体
    availableAgents: [
      { id: "1", name: "通用客服助手", status: "运行中", description: "处理常见客服问题" },
      { id: "2", name: "投诉处理专员", status: "运行中", description: "专门处理投诉和纠纷" },
      { id: "3", name: "产品咨询助手", status: "维护中", description: "提供产品相关咨询" },
      { id: "4", name: "技术支持专员", status: "运行中", description: "解决技术问题" }
    ]
  }
};

export default function ApplicationEdit() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const app = applicationData[id as keyof typeof applicationData];
  
  const [formData, setFormData] = useState({
    name: app?.name || "",
    description: app?.description || "",
    key: app?.key || "",
    settings: app?.settings || {
      autoReply: true,
      maxConcurrentSessions: 100,
      sessionTimeout: 30,
      enableLogging: true,
      enableAnalytics: true,
      rateLimitEnabled: true,
      rateLimitRequests: 1000,
      rateLimitWindow: 60
    },
    security: app?.security || {
      requireAuth: false,
      allowedOrigins: [] as string[],
      ipWhitelist: "",
      enableCors: true
    },
    ui: app?.ui || {
      icon: "",
      signOutUrl: "",
      theme: "default",
      aiAvatar: "",
      userAvatar: ""
    },
    starterPrompts: app?.starterPrompts || [],
    selectedAgentId: app?.selectedAgentId || ""
  });

  const [activeTab, setActiveTab] = useState("basic");

  if (!app) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">应用不存在</h2>
          <p className="text-muted-foreground mb-4">未找到指定的应用信息</p>
          <Link to="/applications">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回应用列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const handleSave = () => {
    // 这里实际应用中会调用API保存数据
    toast({
      title: "保存成功",
      description: "应用配置已更新",
    });
    navigate(`/applications/${id}`);
  };

  const handleDelete = () => {
    // 这里实际应用中会调用API删除应用
    toast({
      title: "删除成功",
      description: "应用已被删除",
      variant: "destructive"
    });
    navigate("/applications");
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to={`/applications/${id}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">编辑应用</h1>
            <p className="text-muted-foreground mt-1">修改应用配置和设置</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge 
            variant={app.status === "运行中" ? "default" : "secondary"}
            className={app.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
          >
            {app.status}
          </Badge>
          <Button onClick={handleSave} className="bg-gradient-primary hover:opacity-90">
            <Save className="mr-2 h-4 w-4" />
            保存更改
          </Button>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="flex space-x-1">
        <Button
          variant={activeTab === "basic" ? "default" : "ghost"}
          onClick={() => setActiveTab("basic")}
          className="h-9"
        >
          基本信息
        </Button>
        <Button
          variant={activeTab === "settings" ? "default" : "ghost"}
          onClick={() => setActiveTab("settings")}
          className="h-9"
        >
          应用设置
        </Button>
        <Button
          variant={activeTab === "security" ? "default" : "ghost"}
          onClick={() => setActiveTab("security")}
          className="h-9"
        >
          安全配置
        </Button>
        <Button
          variant={activeTab === "ui" ? "default" : "ghost"}
          onClick={() => setActiveTab("ui")}
          className="h-9"
        >
          界面配置
        </Button>
        <Button
          variant={activeTab === "prompts" ? "default" : "ghost"}
          onClick={() => setActiveTab("prompts")}
          className="h-9"
        >
          推荐提示词
        </Button>
        <Button
          variant={activeTab === "agent" ? "default" : "ghost"}
          onClick={() => setActiveTab("agent")}
          className="h-9"
        >
          智能体配置
        </Button>
        <Button
          variant={activeTab === "danger" ? "default" : "ghost"}
          onClick={() => setActiveTab("danger")}
          className="h-9 text-destructive"
        >
          危险操作
        </Button>
      </div>

      {/* 基本信息 */}
      {activeTab === "basic" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">基本信息</CardTitle>
            <CardDescription>应用的基本配置信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="app-name">应用名称</Label>
                <Input
                  id="app-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="app-desc">应用描述</Label>
                <Textarea
                  id="app-desc"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  className="bg-muted border-border min-h-[100px]"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="app-key">应用Key</Label>
                <Input
                  id="app-key"
                  value={formData.key}
                  onChange={(e) => setFormData({...formData, key: e.target.value})}
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  应用的唯一标识符
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="app-id">应用ID</Label>
                <Input
                  id="app-id"
                  value={app.id}
                  disabled
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  应用ID创建后不可修改
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="created-at">创建时间</Label>
                <Input
                  id="created-at"
                  value={app.createdAt}
                  disabled
                  className="bg-muted border-border"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 应用设置 */}
      {activeTab === "settings" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">应用设置</CardTitle>
            </div>
            <CardDescription>配置应用的功能和性能参数</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>自动回复</Label>
                  <p className="text-xs text-muted-foreground">
                    启用智能体自动回复功能
                  </p>
                </div>
                <Switch 
                  checked={formData.settings.autoReply}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      settings: {...formData.settings, autoReply: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用日志记录</Label>
                  <p className="text-xs text-muted-foreground">
                    记录所有对话和操作日志
                  </p>
                </div>
                <Switch 
                  checked={formData.settings.enableLogging}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      settings: {...formData.settings, enableLogging: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用数据分析</Label>
                  <p className="text-xs text-muted-foreground">
                    收集和分析使用数据
                  </p>
                </div>
                <Switch 
                  checked={formData.settings.enableAnalytics}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      settings: {...formData.settings, enableAnalytics: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="grid gap-4 md:grid-cols-2">
                <div className="grid gap-2">
                  <Label htmlFor="max-sessions">最大并发会话数</Label>
                  <Input
                    id="max-sessions"
                    type="number"
                    value={formData.settings.maxConcurrentSessions}
                    onChange={(e) => setFormData({
                      ...formData, 
                      settings: {...formData.settings, maxConcurrentSessions: parseInt(e.target.value)}
                    })}
                    className="bg-muted border-border"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="session-timeout">会话超时时间（分钟）</Label>
                  <Input
                    id="session-timeout"
                    type="number"
                    value={formData.settings.sessionTimeout}
                    onChange={(e) => setFormData({
                      ...formData, 
                      settings: {...formData.settings, sessionTimeout: parseInt(e.target.value)}
                    })}
                    className="bg-muted border-border"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 安全配置 */}
      {activeTab === "security" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">安全配置</CardTitle>
            </div>
            <CardDescription>配置应用的安全和访问控制</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用身份验证</Label>
                  <p className="text-xs text-muted-foreground">
                    要求用户登录后才能访问
                  </p>
                </div>
                <Switch 
                  checked={formData.security.requireAuth}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      security: {...formData.security, requireAuth: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用CORS</Label>
                  <p className="text-xs text-muted-foreground">
                    允许跨域请求
                  </p>
                </div>
                <Switch 
                  checked={formData.security.enableCors}
                  onCheckedChange={(checked) => 
                    setFormData({
                      ...formData, 
                      security: {...formData.security, enableCors: checked}
                    })
                  }
                />
              </div>
              <Separator />
              <div className="grid gap-2">
                <Label htmlFor="allowed-origins">允许的域名</Label>
                <Textarea
                  id="allowed-origins"
                  value={formData.security.allowedOrigins?.join('\n')}
                  onChange={(e) => setFormData({
                    ...formData, 
                    security: {...formData.security, allowedOrigins: e.target.value.split('\n').filter(s => s.trim())}
                  })}
                  placeholder="每行一个域名，例如：&#10;https://company.com&#10;https://support.company.com"
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="ip-whitelist">IP白名单</Label>
                <Textarea
                  id="ip-whitelist"
                  value={formData.security.ipWhitelist}
                  onChange={(e) => setFormData({
                    ...formData, 
                    security: {...formData.security, ipWhitelist: e.target.value}
                  })}
                  placeholder="每行一个IP地址或CIDR，例如：&#10;***********/24&#10;********"
                  className="bg-muted border-border"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 界面配置 */}
      {activeTab === "ui" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Palette className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">界面配置</CardTitle>
            </div>
            <CardDescription>配置应用的界面外观和用户体验</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6">
              <div className="grid gap-2">
                <Label htmlFor="app-icon">应用程序图标</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="app-icon"
                    value={formData.ui.icon}
                    onChange={(e) => setFormData({
                      ...formData, 
                      ui: {...formData.ui, icon: e.target.value}
                    })}
                    placeholder="图标URL或上传文件"
                    className="bg-muted border-border"
                  />
                  <Button variant="outline" size="sm">
                    <Upload className="mr-2 h-4 w-4" />
                    上传
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  支持PNG、JPG、SVG格式，建议尺寸64x64px
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="signout-url">应用程序退出URL</Label>
                <Input
                  id="signout-url"
                  value={formData.ui.signOutUrl}
                  onChange={(e) => setFormData({
                    ...formData, 
                    ui: {...formData.ui, signOutUrl: e.target.value}
                  })}
                  placeholder="https://example.com/signout"
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  用户退出应用后跳转的URL地址
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="theme">应用程序的主题</Label>
                <Input
                  id="theme"
                  value={formData.ui.theme}
                  onChange={(e) => setFormData({
                    ...formData, 
                    ui: {...formData.ui, theme: e.target.value}
                  })}
                  placeholder="default, dark, light 等"
                  className="bg-muted border-border"
                />
                <p className="text-xs text-muted-foreground">
                  应用的视觉主题设置
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="ai-avatar">应用程序AI头像</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="ai-avatar"
                    value={formData.ui.aiAvatar}
                    onChange={(e) => setFormData({
                      ...formData, 
                      ui: {...formData.ui, aiAvatar: e.target.value}
                    })}
                    placeholder="AI头像URL或上传文件"
                    className="bg-muted border-border"
                  />
                  <Button variant="outline" size="sm">
                    <Upload className="mr-2 h-4 w-4" />
                    上传
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  AI智能体的头像图片
                </p>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="user-avatar">应用程序用户头像</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="user-avatar"
                    value={formData.ui.userAvatar}
                    onChange={(e) => setFormData({
                      ...formData, 
                      ui: {...formData.ui, userAvatar: e.target.value}
                    })}
                    placeholder="用户头像URL或上传文件"
                    className="bg-muted border-border"
                  />
                  <Button variant="outline" size="sm">
                    <Upload className="mr-2 h-4 w-4" />
                    上传
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  用户的默认头像图片
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 推荐提示词 */}
      {activeTab === "prompts" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">推荐提示词</CardTitle>
            </div>
            <CardDescription>配置用户初次访问时显示的推荐问题</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {formData.starterPrompts.map((prompt, index) => (
                <Card key={prompt.id} className="bg-muted/50 border-border">
                  <CardContent className="pt-3 pb-3">
                    <div className="grid gap-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">提示词 {index + 1}</Label>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const newPrompts = formData.starterPrompts.filter((_, i) => i !== index);
                            setFormData({...formData, starterPrompts: newPrompts});
                          }}
                          className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="grid gap-1.5">
                        <Label htmlFor={`prompt-title-${index}`} className="text-xs">标题</Label>
                        <Input
                          id={`prompt-title-${index}`}
                          value={prompt.title}
                          onChange={(e) => {
                            const newPrompts = [...formData.starterPrompts];
                            newPrompts[index] = {...prompt, title: e.target.value};
                            setFormData({...formData, starterPrompts: newPrompts});
                          }}
                          placeholder="输入提示词标题"
                          className="bg-background border-border h-8"
                        />
                      </div>
                      <div className="grid gap-1.5">
                        <Label htmlFor={`prompt-content-${index}`} className="text-xs">内容</Label>
                        <Textarea
                          id={`prompt-content-${index}`}
                          value={prompt.content}
                          onChange={(e) => {
                            const newPrompts = [...formData.starterPrompts];
                            newPrompts[index] = {...prompt, content: e.target.value};
                            setFormData({...formData, starterPrompts: newPrompts});
                          }}
                          placeholder="输入提示词内容"
                          className="bg-background border-border min-h-[60px] text-sm"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              <Button
                variant="outline"
                onClick={() => {
                  const newPrompt = {
                    id: Date.now().toString(),
                    title: "",
                    content: ""
                  };
                  setFormData({
                    ...formData,
                    starterPrompts: [...formData.starterPrompts, newPrompt]
                  });
                }}
                className="w-full border-dashed border-border hover:border-primary h-9"
              >
                <Plus className="mr-2 h-3 w-3" />
                添加新的提示词
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 智能体配置 */}
      {activeTab === "agent" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-primary" />
              <CardTitle className="text-foreground">智能体配置</CardTitle>
            </div>
            <CardDescription>选择应用关联的智能体</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="agent-select">选择智能体</Label>
                <Select 
                  value={formData.selectedAgentId} 
                  onValueChange={(value) => setFormData({...formData, selectedAgentId: value})}
                >
                  <SelectTrigger className="bg-muted border-border">
                    <SelectValue placeholder="请选择一个智能体" />
                  </SelectTrigger>
                  <SelectContent className="bg-card border-border shadow-lg z-50">
                    {app?.availableAgents?.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id} className="hover:bg-muted">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                            <Bot className="w-4 h-4 text-primary-foreground" />
                          </div>
                          <div className="flex flex-col">
                            <span className="font-medium text-foreground">{agent.name}</span>
                            <span className="text-xs text-muted-foreground">{agent.description}</span>
                          </div>
                          <div className="ml-auto">
                            <Badge 
                              variant={agent.status === "运行中" ? "default" : "secondary"}
                              className={agent.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                            >
                              {agent.status}
                            </Badge>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  选择的智能体将处理此应用的所有对话请求
                </p>
              </div>
              
              {formData.selectedAgentId && (
                <Card className="bg-muted/50 border-border">
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Bot className="h-4 w-4 text-primary" />
                        <Label className="text-sm font-medium">当前选择的智能体</Label>
                      </div>
                      {(() => {
                        const selectedAgent = app?.availableAgents?.find(agent => agent.id === formData.selectedAgentId);
                        return selectedAgent ? (
                          <div className="grid gap-2">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium text-foreground">{selectedAgent.name}</p>
                                <p className="text-sm text-muted-foreground">{selectedAgent.description}</p>
                              </div>
                              <Badge 
                                variant={selectedAgent.status === "运行中" ? "default" : "secondary"}
                                className={selectedAgent.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                              >
                                {selectedAgent.status}
                              </Badge>
                            </div>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 危险操作 */}
      {activeTab === "danger" && (
        <Card className="bg-card border-border shadow-card border-destructive/50">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <CardTitle className="text-foreground">危险操作</CardTitle>
            </div>
            <CardDescription>
              这些操作将对应用产生不可逆的影响，请谨慎操作
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <h4 className="font-medium text-foreground mb-2">删除应用</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  删除应用将同时删除所有相关的智能体、会话记录和配置。此操作不可撤销。
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive">
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除应用
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-card border-border">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-foreground">确认删除应用</AlertDialogTitle>
                      <AlertDialogDescription>
                        您即将删除应用 "{app.name}"。此操作将：
                        <ul className="list-disc list-inside mt-2 space-y-1">
                          <li>删除所有智能体配置</li>
                          <li>删除所有会话记录</li>
                          <li>删除所有相关数据</li>
                        </ul>
                        <br />
                        <strong>此操作不可撤销，请确认是否继续？</strong>
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/90">
                        确认删除
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}