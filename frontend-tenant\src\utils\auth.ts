import Storage, { StorageType } from './storage';

// 存储键常量
const TOKEN_DATA_KEY = 'token_data';
const REMEMBER_ME_KEY = 'remember_me';

// token 数据类型
export interface TokenData {
  accessToken: string;
  refreshToken: string;
  expiresIn: number; // 单位秒
  timestamp: number; // 存储时间
}

/**
 * 身份验证工具类
 * 管理登录状态、Token、记住我等
 */
export class Auth {
  private static _cache: TokenData | null = null;

  /**
   * 设置 token
   */
  static setTokens(
    accessToken: string,
    refreshToken: string,
    expiresIn: number,
    rememberMe: boolean = false
  ): void {
    const tokenData: TokenData = {
      accessToken,
      refreshToken,
      expiresIn,
      timestamp: Date.now()
    };

    const storageType: StorageType = rememberMe ? 'localStorage' : 'sessionStorage';

    // 保存 tokenData
    Storage.setItem(TOKEN_DATA_KEY, tokenData, storageType);
    // 保存记住我
    Storage.setItem(REMEMBER_ME_KEY, rememberMe, 'localStorage');

    // 清理另一种 storage
    const otherType: StorageType = rememberMe ? 'sessionStorage' : 'localStorage';
    Storage.removeItem(TOKEN_DATA_KEY, otherType);

    // 更新缓存
    this._cache = tokenData;
  }

  /**
   * 获取 tokenData
   */
  static getTokenData(): TokenData | null {
    if (this._cache) return this._cache;

    let tokenData = Storage.getItem<TokenData>(TOKEN_DATA_KEY, 'localStorage');
    if (!tokenData) {
      tokenData = Storage.getItem<TokenData>(TOKEN_DATA_KEY, 'sessionStorage');
    }

    this._cache = tokenData || null;
    return this._cache;
  }

  /**
   * 获取 accessToken
   */
  static getAccessToken(): string | null {
    return this.getTokenData()?.accessToken || null;
  }

  /**
   * 获取 refreshToken
   */
  static getRefreshToken(): string | null {
    return this.getTokenData()?.refreshToken || null;
  }

  /**
   * 更新 accessToken（刷新 token 后调用）
   */
  static updateAccessToken(accessToken: string, expiresIn: number): void {
    const tokenData = this.getTokenData();
    if (!tokenData) return;

    const updated: TokenData = {
      ...tokenData,
      accessToken,
      expiresIn,
      timestamp: Date.now()
    };

    const rememberMe = this.getRememberMe();
    const storageType: StorageType = rememberMe ? 'localStorage' : 'sessionStorage';

    Storage.setItem(TOKEN_DATA_KEY, updated, storageType);
    this._cache = updated;
  }

  /**
   * 检查是否已登录
   */
  static isLoggedIn(): boolean {
    const tokenData = this.getTokenData();
    if (!tokenData) return false;

    const now = Date.now();
    const expireTime = tokenData.timestamp + tokenData.expiresIn * 1000;
    if (now >= expireTime) {
      this.clearTokens();
      return false;
    }
    return true;
  }

  /**
   * 判断 token 是否即将过期（5分钟内）
   */
  static isTokenExpiringSoon(): boolean {
    const tokenData = this.getTokenData();
    if (!tokenData) return false;

    const now = Date.now();
    const expireTime = tokenData.timestamp + tokenData.expiresIn * 1000;
    const fiveMinutes = 5 * 60 * 1000;

    return expireTime - now <= fiveMinutes;
  }

  /**
   * 获取记住我状态
   */
  static getRememberMe(): boolean {
    return Storage.getItem<boolean>(REMEMBER_ME_KEY, 'localStorage') || false;
  }

  /**
   * 清除 token 和登录状态
   */
  static clearTokens(): void {
    Storage.removeItem(TOKEN_DATA_KEY, 'localStorage');
    Storage.removeItem(TOKEN_DATA_KEY, 'sessionStorage');
    Storage.removeItem(REMEMBER_ME_KEY, 'localStorage');
    this._cache = null;
  }
}
