import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Search, 
  MoreHorizontal, 
  Eye, 
  MessageSquare,
  Clock,
  User,
  Bot,
  Users,
  TrendingUp,
  MapPin,
  Globe,
  Calendar
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";

// 使用智能体的用户数据（外部用户/客户）
const conversationUsers = [
  {
    id: "user_001",
    clientId: "用户001",
    avatar: null,
    nickname: "张先生",
    firstSessionAt: "2024-01-15 09:30:00",
    lastSessionAt: "2024-01-20 14:35:00",
    totalSessions: 8,
    totalMessages: 45,
    favoriteAgent: "通用客服助手",
    mostUsedApp: "客服机器人",
    status: "活跃",
    ipAddress: "*************",
    userAgent: "Chrome/120.0.0.0",
    location: "上海"
  },
  {
    id: "user_002", 
    clientId: "用户002",
    avatar: null,
    nickname: "李小姐",
    firstSessionAt: "2024-01-10 16:20:00",
    lastSessionAt: "2024-01-20 14:28:00",
    totalSessions: 12,
    totalMessages: 67,
    favoriteAgent: "销售顾问",
    mostUsedApp: "销售助手",
    status: "活跃",
    ipAddress: "*************",
    userAgent: "Safari/17.0",
    location: "北京"
  },
  {
    id: "user_003",
    clientId: "用户003", 
    avatar: null,
    nickname: "王工程师",
    firstSessionAt: "2024-01-08 11:15:00",
    lastSessionAt: "2024-01-20 14:28:00",
    totalSessions: 15,
    totalMessages: 89,
    favoriteAgent: "技术诊断专家",
    mostUsedApp: "技术支持",
    status: "活跃",
    ipAddress: "*************",
    userAgent: "Firefox/121.0",
    location: "深圳"
  },
  {
    id: "user_004",
    clientId: "用户004",
    avatar: null,
    nickname: "陈总监",
    firstSessionAt: "2024-01-12 13:45:00",
    lastSessionAt: "2024-01-19 16:30:00",
    totalSessions: 6,
    totalMessages: 28,
    favoriteAgent: "通用客服助手",
    mostUsedApp: "客服机器人",
    status: "一般",
    ipAddress: "*************",
    userAgent: "Chrome/120.0.0.0",
    location: "广州"
  },
  {
    id: "user_005",
    clientId: "用户005",
    avatar: null,
    nickname: "刘博士",
    firstSessionAt: "2024-01-05 10:20:00",
    lastSessionAt: "2024-01-18 15:45:00",
    totalSessions: 3,
    totalMessages: 12,
    favoriteAgent: "技术诊断专家",
    mostUsedApp: "技术支持",
    status: "沉寂",
    ipAddress: "*************",
    userAgent: "Edge/120.0.0.0",
    location: "杭州"
  },
  {
    id: "user_006",
    clientId: "用户006",
    avatar: null,
    nickname: "赵经理",
    firstSessionAt: "2024-01-18 14:20:00",
    lastSessionAt: "2024-01-20 16:15:00",
    totalSessions: 4,
    totalMessages: 22,
    favoriteAgent: "销售顾问",
    mostUsedApp: "销售助手",
    status: "活跃",
    ipAddress: "*************",
    userAgent: "Chrome/120.0.0.0",
    location: "成都"
  }
];

// 示例用户详细信息
const sampleUserSessions = [
  {
    id: "session_1",
    application: "客服机器人",
    agent: "通用客服助手",
    startTime: "2024-01-20 14:30:00",
    endTime: "2024-01-20 14:35:00",
    messageCount: 6,
    status: "已结束"
  },
  {
    id: "session_2",
    application: "客服机器人",
    agent: "通用客服助手",
    startTime: "2024-01-19 16:20:00",
    endTime: "2024-01-19 16:25:00",
    messageCount: 8,
    status: "已结束"
  }
];

export default function SessionUsers() {
  const [userSearchTerm, setUserSearchTerm] = useState("");
  const [selectedUserStatus, setSelectedUserStatus] = useState("");
  const [selectedUser, setSelectedUser] = useState<typeof conversationUsers[0] | null>(null);

  const filteredUsers = conversationUsers.filter(user => {
    const matchesSearch = user.clientId.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
                         user.nickname.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
                         user.favoriteAgent.toLowerCase().includes(userSearchTerm.toLowerCase());
    const matchesStatus = !selectedUserStatus || selectedUserStatus === "all" || user.status === selectedUserStatus;
    
    return matchesSearch && matchesStatus;
  });

  const userStatuses = ["活跃", "一般", "沉寂"];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">会话用户</h1>
          <p className="text-muted-foreground mt-1">查看使用智能体进行会话的用户统计和详情</p>
        </div>
      </div>

      {/* 用户统计卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总用户数
            </CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{conversationUsers.length}</div>
            <p className="text-xs text-muted-foreground">
              累计会话用户
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              活跃用户
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {conversationUsers.filter(u => u.status === "活跃").length}
            </div>
            <p className="text-xs text-muted-foreground">
              最近活跃用户
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              平均会话数
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {Math.round(conversationUsers.reduce((sum, u) => sum + u.totalSessions, 0) / conversationUsers.length)}
            </div>
            <p className="text-xs text-muted-foreground">
              每用户平均
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总消息数
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {conversationUsers.reduce((sum, u) => sum + u.totalMessages, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              累计用户消息
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 用户列表 */}
      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-foreground">用户列表</CardTitle>
              <CardDescription>
                管理和查看使用智能体的用户信息
              </CardDescription>
            </div>
          </div>
          {/* 搜索和筛选 */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索用户ID、昵称或智能体..."
                value={userSearchTerm}
                onChange={(e) => setUserSearchTerm(e.target.value)}
                className="pl-10 bg-muted border-border"
              />
            </div>
            <Select value={selectedUserStatus} onValueChange={setSelectedUserStatus}>
              <SelectTrigger className="w-32 bg-muted border-border">
                <SelectValue placeholder="用户状态" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                <SelectItem value="all">全部状态</SelectItem>
                {userStatuses.map((status) => (
                  <SelectItem key={status} value={status}>{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="text-muted-foreground">用户信息</TableHead>
                <TableHead className="text-muted-foreground">会话统计</TableHead>
                <TableHead className="text-muted-foreground">偏好智能体</TableHead>
                <TableHead className="text-muted-foreground">状态</TableHead>
                <TableHead className="text-muted-foreground">位置</TableHead>
                <TableHead className="text-muted-foreground">最后活跃</TableHead>
                <TableHead className="text-muted-foreground text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id} className="border-border hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-primary-foreground" />
                      </div>
                      <div>
                        <div className="font-semibold text-foreground">{user.nickname}</div>
                        <div className="text-sm text-muted-foreground">{user.clientId}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium text-foreground">{user.totalSessions} 次会话</div>
                      <div className="text-sm text-muted-foreground">{user.totalMessages} 条消息</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium text-foreground flex items-center">
                        <Bot className="w-3 h-3 mr-1" />
                        {user.favoriteAgent}
                      </div>
                      <div className="text-sm text-muted-foreground">{user.mostUsedApp}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={user.status === "活跃" ? "default" : user.status === "一般" ? "secondary" : "outline"}
                      className={
                        user.status === "活跃" ? "bg-green-500/20 text-green-500 border-green-500/30" : 
                        user.status === "一般" ? "bg-yellow-500/20 text-yellow-600 border-yellow-500/30" :
                        "bg-gray-500/20 text-gray-500 border-gray-500/30"
                      }
                    >
                      {user.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-muted-foreground">
                      <MapPin className="mr-1 h-3 w-3" />
                      {user.location}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-muted-foreground">
                      <Clock className="mr-1 h-3 w-3" />
                      {user.lastSessionAt.split(' ')[1]}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-popover border-border">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => setSelectedUser(user)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <Link to={`/session-users/${user.id}/history`}>
                          <DropdownMenuItem>
                            <MessageSquare className="mr-2 h-4 w-4" />
                            查看会话历史
                          </DropdownMenuItem>
                        </Link>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 用户详情对话框 */}
      <Dialog open={!!selectedUser} onOpenChange={() => setSelectedUser(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] bg-card border-border">
          <DialogHeader>
            <DialogTitle className="text-foreground">用户详情</DialogTitle>
            <DialogDescription>
              查看用户的详细信息和会话历史
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 用户基本信息 */}
              <div className="space-y-4">
                <div className="flex items-center space-x-4 p-4 bg-muted/30 rounded-lg">
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center">
                    <User className="w-8 h-8 text-primary-foreground" />
                  </div>
                  <div>
                    <div className="font-semibold text-lg text-foreground">{selectedUser.nickname}</div>
                    <div className="text-sm text-muted-foreground">{selectedUser.clientId}</div>
                    <Badge 
                      variant={selectedUser.status === "活跃" ? "default" : selectedUser.status === "一般" ? "secondary" : "outline"}
                      className="mt-1"
                    >
                      {selectedUser.status}
                    </Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium text-foreground">基本信息</h4>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">首次访问:</span>
                      <span className="text-foreground">{selectedUser.firstSessionAt}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">最后活跃:</span>
                      <span className="text-foreground">{selectedUser.lastSessionAt}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">总会话数:</span>
                      <span className="text-foreground">{selectedUser.totalSessions}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">总消息数:</span>
                      <span className="text-foreground">{selectedUser.totalMessages}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">偏好智能体:</span>
                      <span className="text-foreground">{selectedUser.favoriteAgent}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">常用应用:</span>
                      <span className="text-foreground">{selectedUser.mostUsedApp}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">IP地址:</span>
                      <span className="text-foreground">{selectedUser.ipAddress}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">浏览器:</span>
                      <span className="text-foreground">{selectedUser.userAgent}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">位置:</span>
                      <span className="text-foreground">{selectedUser.location}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 会话历史 */}
              <div className="lg:col-span-2">
                <h4 className="font-medium text-foreground mb-4">会话历史</h4>
                <ScrollArea className="h-96 bg-muted/20 rounded-lg p-4">
                  <div className="space-y-4">
                    {sampleUserSessions.map((session, index) => (
                      <div key={index} className="p-4 bg-card border border-border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Bot className="w-4 h-4 text-primary" />
                            <span className="font-medium text-foreground">{session.agent}</span>
                          </div>
                          <Badge variant={session.status === "进行中" ? "default" : "secondary"}>
                            {session.status}
                          </Badge>
                        </div>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">应用:</span>
                            <span className="text-foreground">{session.application}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">开始时间:</span>
                            <span className="text-foreground">{session.startTime}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">结束时间:</span>
                            <span className="text-foreground">{session.endTime}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">消息数:</span>
                            <span className="text-foreground">{session.messageCount} 条</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}