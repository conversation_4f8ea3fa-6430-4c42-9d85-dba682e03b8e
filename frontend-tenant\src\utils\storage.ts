export type StorageType = 'localStorage' | 'sessionStorage';

class Storage {
  /**
   * 设置存储项
   * @param key 存储键
   * @param value 存储值
   * @param type 存储类型，默认 localStorage
   */
  static setItem<T>(key: string, value: T, type: StorageType = 'localStorage'): void {
    try {
      const storage = type === 'localStorage' ? localStorage : sessionStorage;
      storage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Storage 设置失败 [${type}] key=${key}:`, error);
    }
  }

  /**
   * 获取存储项
   * @param key 存储键
   * @param type 存储类型，默认 localStorage
   * @returns 存储值或 null
   */
  static getItem<T>(key: string, type: StorageType = 'localStorage'): T | null {
    try {
      const storage = type === 'localStorage' ? localStorage : sessionStorage;
      const item = storage.getItem(key);
      if (!item) return null;
      return JSON.parse(item) as T;
    } catch (error) {
      console.error(`Storage 获取失败 [${type}] key=${key}:`, error);
      return null;
    }
  }

  /**
   * 删除存储项
   * @param key 存储键
   * @param type 存储类型，默认 localStorage
   */
  static removeItem(key: string, type: StorageType = 'localStorage'): void {
    try {
      const storage = type === 'localStorage' ? localStorage : sessionStorage;
      storage.removeItem(key);
    } catch (error) {
      console.error(`Storage 删除失败 [${type}] key=${key}:`, error);
    }
  }

  /**
   * 清空指定类型存储
   * @param type 存储类型，默认 localStorage
   */
  static clear(type: StorageType = 'localStorage'): void {
    try {
      const storage = type === 'localStorage' ? localStorage : sessionStorage;
      storage.clear();
    } catch (error) {
      console.error(`Storage 清空失败 [${type}]:`, error);
    }
  }

  /**
   * 检查存储项是否存在
   * @param key 存储键
   * @param type 存储类型，默认 localStorage
   * @returns 是否存在
   */
  static hasItem(key: string, type: StorageType = 'localStorage'): boolean {
    try {
      const storage = type === 'localStorage' ? localStorage : sessionStorage;
      return storage.getItem(key) !== null;
    } catch (error) {
      console.error(`Storage 检查失败 [${type}] key=${key}:`, error);
      return false;
    }
  }
}

export default Storage;
