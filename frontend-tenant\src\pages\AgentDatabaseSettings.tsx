import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  Save, 
  Database,
  Key,
  RefreshCw,
  TestTube,
  Shield,
  Clock,
  AlertCircle,
  CheckCircle,
  Settings,
  History,
  Users,
  Lock
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

// 模拟数据 - 实际应用中从API获取
const agentData = {
  "1": {
    id: "1",
    name: "通用客服助手",
    description: "处理常见客户咨询和问题解答",
    database: {
      dbType: "MySql",
      host: "localhost",
      port: "3306",
      dbName: "customer_db",
      username: "dbuser",
      password: "****",
      connectionString: "Server=localhost;Database=customer_db;Uid=dbuser;Pwd=****;",
      ssl: true,
      connectionPool: {
        minConnections: 5,
        maxConnections: 20,
        connectionTimeout: 30
      },
      cachedSchema: `CREATE TABLE customers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE,
  phone VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE orders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  customer_id INT,
  order_date DATE,
  total_amount DECIMAL(10,2),
  status ENUM('pending', 'completed', 'cancelled'),
  FOREIGN KEY (customer_id) REFERENCES customers(id)
);`,
      cachedAt: "2024-01-20T10:30:00Z",
      lastConnectionTest: "2024-01-20T14:45:00Z",
      connectionStatus: "成功",
      permissions: {
        select: true,
        insert: false,
        update: false,
        delete: false
      }
    },
    statistics: {
      totalQueries: 1250,
      successfulQueries: 1205,
      failedQueries: 45,
      avgQueryTime: "125ms",
      lastQuery: "2024-01-20T15:30:00Z"
    }
  }
};

const dbTypes = [
  { value: "MySql", label: "MySQL", port: "3306" },
  { value: "PostgreSql", label: "PostgreSQL", port: "5432" },
  { value: "Sqlite", label: "SQLite", port: "" },
  { value: "MSSql", label: "SQL Server", port: "1433" },
  { value: "Oracle", label: "Oracle", port: "1521" }
];

export default function AgentDatabaseSettings() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const agent = agentData[id as keyof typeof agentData];
  
  const [formData, setFormData] = useState({
    dbType: agent?.database?.dbType || "MySql",
    host: agent?.database?.host || "",
    port: agent?.database?.port || "3306",
    dbName: agent?.database?.dbName || "",
    username: agent?.database?.username || "",
    password: agent?.database?.password || "",
    connectionString: agent?.database?.connectionString || "",
    ssl: agent?.database?.ssl || false,
    connectionPool: {
      minConnections: agent?.database?.connectionPool?.minConnections || 5,
      maxConnections: agent?.database?.connectionPool?.maxConnections || 20,
      connectionTimeout: agent?.database?.connectionPool?.connectionTimeout || 30
    },
    permissions: agent?.database?.permissions || {
      select: true,
      insert: false,
      update: false,
      delete: false
    }
  });

  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isRefreshingSchema, setIsRefreshingSchema] = useState(false);
  const [connectionTestResult, setConnectionTestResult] = useState<string | null>(null);

  if (!agent) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">智能体不存在</h2>
          <p className="text-muted-foreground mb-4">未找到指定的智能体信息</p>
          <Link to="/agents">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回智能体列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const handleSave = () => {
    toast({
      title: "保存成功",
      description: "数据库配置已更新",
    });
  };

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    setConnectionTestResult(null);
    
    // 模拟测试数据库连接
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const success = Math.random() > 0.3; // 70% 成功率
    
    if (success) {
      setConnectionTestResult("成功");
      toast({
        title: "连接测试成功",
        description: "数据库连接正常，所有配置有效",
      });
    } else {
      setConnectionTestResult("失败");
      toast({
        title: "连接测试失败",
        description: "无法连接到数据库，请检查配置",
        variant: "destructive"
      });
    }
    
    setIsTestingConnection(false);
  };

  const handleRefreshSchema = async () => {
    setIsRefreshingSchema(true);
    
    // 模拟刷新架构
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    setIsRefreshingSchema(false);
    toast({
      title: "架构刷新成功",
      description: "数据库架构已更新到最新版本",
    });
  };

  const selectedDbType = dbTypes.find(type => type.value === formData.dbType);

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/agents">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Database className="w-5 h-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-foreground">数据库设置</h1>
              <p className="text-muted-foreground mt-1">配置 {agent.name} 的数据库连接和权限</p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge 
            variant={agent.database.connectionStatus === "成功" ? "default" : "destructive"}
            className={agent.database.connectionStatus === "成功" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
          >
            <Database className="mr-1 h-3 w-3" />
            {agent.database.connectionStatus}
          </Badge>
          <Button onClick={handleSave} className="bg-gradient-primary hover:opacity-90">
            <Save className="mr-2 h-4 w-4" />
            保存配置
          </Button>
        </div>
      </div>

      {/* 数据库状态概览 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总查询数
            </CardTitle>
            <Database className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{agent.statistics.totalQueries.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              累计执行
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              成功率
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {((agent.statistics.successfulQueries / agent.statistics.totalQueries) * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              查询成功率
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              平均响应
            </CardTitle>
            <Clock className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{agent.statistics.avgQueryTime}</div>
            <p className="text-xs text-muted-foreground">
              查询时间
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              失败查询
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{agent.statistics.failedQueries}</div>
            <p className="text-xs text-muted-foreground">
              错误次数
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要配置区域 */}
      <Tabs defaultValue="connection" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="connection" className="flex items-center space-x-2">
            <Key className="h-4 w-4" />
            <span>连接配置</span>
          </TabsTrigger>
          <TabsTrigger value="schema" className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>数据库架构</span>
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>权限设置</span>
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>高级配置</span>
          </TabsTrigger>
        </TabsList>

        {/* 连接配置 */}
        <TabsContent value="connection" className="space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">数据库连接信息</CardTitle>
              <CardDescription>配置数据库的基本连接参数</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="grid gap-2">
                  <Label htmlFor="db-type">数据库类型</Label>
                  <Select 
                    value={formData.dbType} 
                    onValueChange={(value) => {
                      const selectedType = dbTypes.find(type => type.value === value);
                      setFormData({
                        ...formData, 
                        dbType: value,
                        port: selectedType?.port || ""
                      });
                    }}
                  >
                    <SelectTrigger className="bg-muted border-border">
                      <SelectValue placeholder="选择数据库类型" />
                    </SelectTrigger>
                    <SelectContent className="bg-popover border-border">
                      {dbTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="host">主机地址</Label>
                  <Input
                    id="host"
                    value={formData.host}
                    onChange={(e) => setFormData({...formData, host: e.target.value})}
                    placeholder="localhost"
                    className="bg-muted border-border"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="port">端口</Label>
                  <Input
                    id="port"
                    value={formData.port}
                    onChange={(e) => setFormData({...formData, port: e.target.value})}
                    placeholder={selectedDbType?.port}
                    className="bg-muted border-border"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="db-name">数据库名</Label>
                  <Input
                    id="db-name"
                    value={formData.dbName}
                    onChange={(e) => setFormData({...formData, dbName: e.target.value})}
                    className="bg-muted border-border"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="username">用户名</Label>
                  <Input
                    id="username"
                    value={formData.username}
                    onChange={(e) => setFormData({...formData, username: e.target.value})}
                    className="bg-muted border-border"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="password">密码</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    className="bg-muted border-border"
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用SSL连接</Label>
                    <p className="text-xs text-muted-foreground">
                      使用加密连接提高安全性
                    </p>
                  </div>
                  <Switch 
                    checked={formData.ssl}
                    onCheckedChange={(checked: boolean) => 
                      setFormData({...formData, ssl: checked})
                    }
                  />
                </div>

                <Separator />

                <div className="grid gap-2">
                  <Label htmlFor="connection-string">完整连接字符串</Label>
                  <Textarea
                    id="connection-string"
                    value={formData.connectionString}
                    onChange={(e) => setFormData({...formData, connectionString: e.target.value})}
                    placeholder="Server=localhost;Database=mydb;Uid=user;Pwd=password;"
                    className="bg-muted border-border"
                  />
                  <p className="text-xs text-muted-foreground">
                    如果提供了连接字符串，将优先使用此配置
                  </p>
                </div>

                <div className="flex items-center justify-between pt-4">
                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      onClick={handleTestConnection}
                      disabled={isTestingConnection}
                    >
                      {isTestingConnection ? (
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <TestTube className="mr-2 h-4 w-4" />
                      )}
                      {isTestingConnection ? "测试中..." : "测试连接"}
                    </Button>
                    {connectionTestResult && (
                      <Badge 
                        variant="outline" 
                        className={connectionTestResult === "成功" 
                          ? "text-green-500 border-green-500/30" 
                          : "text-red-500 border-red-500/30"
                        }
                      >
                        {connectionTestResult === "成功" ? (
                          <CheckCircle className="mr-1 h-3 w-3" />
                        ) : (
                          <AlertCircle className="mr-1 h-3 w-3" />
                        )}
                        连接{connectionTestResult}
                      </Badge>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    最后测试：{new Date(agent.database.lastConnectionTest).toLocaleString()}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 数据库架构 */}
        <TabsContent value="schema" className="space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-foreground">数据库架构</CardTitle>
                  <CardDescription>缓存的数据库表结构信息</CardDescription>
                </div>
                <Button 
                  variant="outline" 
                  onClick={handleRefreshSchema}
                  disabled={isRefreshingSchema}
                >
                  {isRefreshingSchema ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-4 w-4" />
                  )}
                  {isRefreshingSchema ? "刷新中..." : "刷新架构"}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="cached-schema">架构定义 (SQL)</Label>
                <Textarea
                  id="cached-schema"
                  value={agent.database.cachedSchema}
                  className="bg-muted border-border min-h-[400px] font-mono text-sm"
                  readOnly
                />
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">
                    <History className="inline mr-1 h-3 w-3" />
                    最后更新：{new Date(agent.database.cachedAt).toLocaleString()}
                  </p>
                  <Badge variant="outline" className="text-green-500 border-green-500/30">
                    <CheckCircle className="mr-1 h-3 w-3" />
                    架构已缓存
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 权限设置 */}
        <TabsContent value="permissions" className="space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">数据库操作权限</CardTitle>
              <CardDescription>设置智能体对数据库的访问权限</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <div className="space-y-0.5">
                    <Label className="text-green-700 dark:text-green-400">查询权限 (SELECT)</Label>
                    <p className="text-xs text-muted-foreground">
                      允许智能体读取数据库中的数据
                    </p>
                  </div>
                  <Switch 
                    checked={formData.permissions.select}
                    onCheckedChange={(checked: boolean) => 
                      setFormData({
                        ...formData, 
                        permissions: {...formData.permissions, select: checked}
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                  <div className="space-y-0.5">
                    <Label className="text-yellow-700 dark:text-yellow-400">插入权限 (INSERT)</Label>
                    <p className="text-xs text-muted-foreground">
                      允许智能体向数据库添加新记录
                    </p>
                  </div>
                  <Switch 
                    checked={formData.permissions.insert}
                    onCheckedChange={(checked: boolean) => 
                      setFormData({
                        ...formData, 
                        permissions: {...formData.permissions, insert: checked}
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                  <div className="space-y-0.5">
                    <Label className="text-orange-700 dark:text-orange-400">更新权限 (UPDATE)</Label>
                    <p className="text-xs text-muted-foreground">
                      允许智能体修改现有的数据记录
                    </p>
                  </div>
                  <Switch 
                    checked={formData.permissions.update}
                    onCheckedChange={(checked: boolean) => 
                      setFormData({
                        ...formData, 
                        permissions: {...formData.permissions, update: checked}
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                  <div className="space-y-0.5">
                    <Label className="text-red-700 dark:text-red-400">删除权限 (DELETE)</Label>
                    <p className="text-xs text-muted-foreground">
                      允许智能体删除数据库中的记录
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch 
                      checked={formData.permissions.delete}
                      onCheckedChange={(checked: boolean) => 
                        setFormData({
                          ...formData, 
                          permissions: {...formData.permissions, delete: checked}
                        })
                      }
                    />
                    {formData.permissions.delete && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm" className="text-red-500 border-red-500/30">
                            <Lock className="h-3 w-3" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-card border-border">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-foreground">确认启用删除权限</AlertDialogTitle>
                            <AlertDialogDescription>
                              启用删除权限是一个高风险操作。智能体将能够删除数据库中的记录，这可能导致数据丢失。
                              <br /><br />
                              请确认您了解这一风险并且确实需要此权限。
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>取消</AlertDialogCancel>
                            <AlertDialogAction className="bg-red-500 hover:bg-red-500/90">
                              确认启用
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <h4 className="font-medium text-foreground mb-2">权限说明</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>查询权限</strong>：建议始终启用，允许智能体读取数据回答问题</li>
                  <li>• <strong>插入权限</strong>：允许智能体创建新记录，如添加客户信息</li>
                  <li>• <strong>更新权限</strong>：允许智能体修改现有数据，如更新订单状态</li>
                  <li>• <strong>删除权限</strong>：高风险操作，仅在必要时启用</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 高级配置 */}
        <TabsContent value="advanced" className="space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">连接池设置</CardTitle>
              <CardDescription>配置数据库连接池参数以优化性能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="grid gap-2">
                  <Label htmlFor="min-connections">最小连接数</Label>
                  <Input
                    id="min-connections"
                    type="number"
                    value={formData.connectionPool.minConnections}
                    onChange={(e) => setFormData({
                      ...formData, 
                      connectionPool: {
                        ...formData.connectionPool, 
                        minConnections: parseInt(e.target.value)
                      }
                    })}
                    className="bg-muted border-border"
                  />
                  <p className="text-xs text-muted-foreground">
                    连接池中保持的最小连接数
                  </p>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="max-connections">最大连接数</Label>
                  <Input
                    id="max-connections"
                    type="number"
                    value={formData.connectionPool.maxConnections}
                    onChange={(e) => setFormData({
                      ...formData, 
                      connectionPool: {
                        ...formData.connectionPool, 
                        maxConnections: parseInt(e.target.value)
                      }
                    })}
                    className="bg-muted border-border"
                  />
                  <p className="text-xs text-muted-foreground">
                    连接池允许的最大连接数
                  </p>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="connection-timeout">连接超时 (秒)</Label>
                  <Input
                    id="connection-timeout"
                    type="number"
                    value={formData.connectionPool.connectionTimeout}
                    onChange={(e) => setFormData({
                      ...formData, 
                      connectionPool: {
                        ...formData.connectionPool, 
                        connectionTimeout: parseInt(e.target.value)
                      }
                    })}
                    className="bg-muted border-border"
                  />
                  <p className="text-xs text-muted-foreground">
                    获取连接的超时时间
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">查询统计</CardTitle>
              <CardDescription>数据库操作的详细统计信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">成功查询</span>
                    <span className="text-sm font-medium">{agent.statistics.successfulQueries.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">失败查询</span>
                    <span className="text-sm font-medium text-red-500">{agent.statistics.failedQueries.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">平均查询时间</span>
                    <span className="text-sm font-medium">{agent.statistics.avgQueryTime}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">最后查询时间</span>
                    <span className="text-sm font-medium">
                      {new Date(agent.statistics.lastQuery).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">成功率</span>
                    <span className="text-sm font-medium text-green-500">
                      {((agent.statistics.successfulQueries / agent.statistics.totalQueries) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}