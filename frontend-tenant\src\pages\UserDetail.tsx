import { useParams, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Edit, 
  Calendar, 
  User, 
  Shield, 
  Clock,
  UserCheck,
  UserX,
  Mail,
  Phone,
  MapPin
} from "lucide-react";

// 模拟数据
const users = [
  {
    id: "1",
    username: "admin",
    nickname: "系统管理员",
    email: "<EMAIL>",
    phone: "13800138000",
    gender: 1,
    status: "Active",
    createTime: "2024-01-10 10:30:00",
    updateTime: "2024-01-20 15:45:00",
    lastLoginTime: "2024-01-25 09:15:00",
    roles: ["管理员", "操作员"],
    department: "技术部",
    position: "系统管理员",
    loginCount: 156,
    description: "负责系统维护和用户管理，具有最高权限"
  },
  {
    id: "2",
    username: "operator01",
    nickname: "客服小张",
    email: "<EMAIL>",
    phone: "13800138001",
    gender: 2,
    status: "Active", 
    createTime: "2024-01-15 14:20:00",
    updateTime: "2024-01-18 16:30:00",
    lastLoginTime: "2024-01-24 08:45:00",
    roles: ["操作员"],
    department: "客服部",
    position: "客服专员",
    loginCount: 89,
    description: "负责客户咨询和问题处理"
  },
  {
    id: "3",
    username: "support01",
    nickname: "技术小李",
    email: "<EMAIL>",
    phone: "13800138002",
    gender: 1,
    status: "Active",
    createTime: "2024-01-20 11:15:00",
    updateTime: "2024-01-22 13:20:00",
    lastLoginTime: "2024-01-25 10:30:00",
    roles: ["技术支持"],
    department: "技术部",
    position: "技术支持工程师",
    loginCount: 45,
    description: "负责技术问题解答和系统故障处理"
  },
  {
    id: "4",
    username: "test_user",
    nickname: "测试账号",
    email: "<EMAIL>",
    phone: "",
    gender: 0,
    status: "Inactive",
    createTime: "2024-02-01 09:00:00",
    updateTime: "2024-02-01 09:00:00",
    lastLoginTime: "从未登录",
    roles: ["测试员"],
    department: "技术部",
    position: "测试员",
    loginCount: 0,
    description: "用于系统功能测试的账号"
  },
];

const genderMap = { 0: "保密", 1: "男", 2: "女" };
const statusMap = { "Active": "正常", "Inactive": "禁用" };

export default function UserDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const user = users.find(u => u.id === id);

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground">成员未找到</h2>
          <p className="text-muted-foreground mt-2">请检查链接是否正确</p>
          <Button onClick={() => navigate("/users")} className="mt-4">
            返回成员列表
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/users")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回列表
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">成员详情</h1>
            <p className="text-muted-foreground mt-1">查看和管理成员信息</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => navigate(`/users/${id}/edit`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            编辑成员
          </Button>
          <Button
            variant={user.status === "Active" ? "destructive" : "default"}
          >
            {user.status === "Active" ? (
              <>
                <UserX className="mr-2 h-4 w-4" />
                禁用成员
              </>
            ) : (
              <>
                <UserCheck className="mr-2 h-4 w-4" />
                启用成员
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* 基本信息 */}
        <div className="md:col-span-2 space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <User className="mr-2 h-5 w-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center">
                  <span className="text-xl font-bold text-primary-foreground">
                    {user.nickname?.charAt(0) || user.username.charAt(0)}
                  </span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-foreground">{user.nickname || user.username}</h3>
                  <p className="text-muted-foreground">@{user.username}</p>
                  <Badge 
                    variant={user.status === "Active" ? "default" : "secondary"}
                    className={user.status === "Active" ? "bg-green-500/20 text-green-500 border-green-500/30" : "bg-red-500/20 text-red-500 border-red-500/30"}
                  >
                    {statusMap[user.status as keyof typeof statusMap]}
                  </Badge>
                </div>
              </div>
              
              <Separator />
              
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Mail className="mr-2 h-4 w-4" />
                    邮箱地址
                  </div>
                  <p className="text-foreground">{user.email}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Phone className="mr-2 h-4 w-4" />
                    联系电话
                  </div>
                  <p className="text-foreground">{user.phone || "未设置"}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <User className="mr-2 h-4 w-4" />
                    性别
                  </div>
                  <p className="text-foreground">{genderMap[user.gender as keyof typeof genderMap]}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="mr-2 h-4 w-4" />
                    部门职位
                  </div>
                  <p className="text-foreground">{user.department} - {user.position}</p>
                </div>
              </div>
              
              {user.description && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">描述信息</div>
                    <p className="text-foreground">{user.description}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* 角色权限 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <Shield className="mr-2 h-5 w-5" />
                角色权限
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {user.roles.map((role, index) => (
                  <Badge key={index} variant="outline" className="px-3 py-1">
                    {role}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 统计信息 */}
        <div className="space-y-6">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <Clock className="mr-2 h-5 w-5" />
                活动统计
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">登录次数</div>
                <div className="text-2xl font-bold text-foreground">{user.loginCount}</div>
              </div>
              <Separator />
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">最后登录</div>
                <div className="text-sm text-foreground">{user.lastLoginTime}</div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <Calendar className="mr-2 h-5 w-5" />
                时间信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">创建时间</div>
                <div className="text-sm text-foreground">{user.createTime}</div>
              </div>
              <Separator />
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">更新时间</div>
                <div className="text-sm text-foreground">{user.updateTime}</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}