import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft, 
  Edit, 
  Activity, 
  Bot, 
  MessageSquare, 
  Calendar,
  Users,
  TrendingUp,
  BarChart3,
  Settings,
  Shield,
  Palette,
  Eye,
  Info
} from "lucide-react";

// 模拟数据 - 实际应用中从API获取
const applicationData = {
  "1": {
    id: "1",
    name: "客服机器人",
    description: "智能客服系统，提供24/7在线服务",
    key: "customer-service-bot-2024",
    status: "运行中",
    createdAt: "2024-01-15",
    settings: {
      autoReply: true,
      maxConcurrentSessions: 100,
      sessionTimeout: 30,
      enableLogging: true,
      enableAnalytics: true,
      rateLimitEnabled: true,
      rateLimitRequests: 1000,
      rateLimitWindow: 60
    },
    security: {
      requireAuth: false,
      allowedOrigins: ["https://company.com", "https://support.company.com"],
      ipWhitelist: "",
      enableCors: true
    },
    ui: {
      icon: "/icon.png",
      signOutUrl: "https://company.com/signout",
      theme: "default",
      aiAvatar: "/ai-avatar.png",
      userAvatar: "/user-avatar.png"
    },
    starterPrompts: [
      {
        id: "1",
        title: "产品咨询",
        content: "我想了解你们的产品和服务"
      },
      {
        id: "2", 
        title: "技术支持",
        content: "我遇到了技术问题，需要帮助"
      },
      {
        id: "3",
        title: "售后服务",
        content: "我需要售后服务支持"
      }
    ],
    agentCount: 3,
    sessionCount: 1234,
    lastActivity: "2分钟前",
    monthlyRequests: 8420,
    successRate: 96.5,
    avgResponseTime: "1.2秒",
    totalUsers: 456,
    agents: [
      { id: "1", name: "通用客服助手", status: "运行中", requests: 5420 },
      { id: "2", name: "投诉处理专员", status: "运行中", requests: 2100 },
      { id: "3", name: "产品咨询助手", status: "维护中", requests: 900 }
    ],
    recentSessions: [
      { id: "1", user: "用户001", agent: "通用客服助手", messages: 12, status: "进行中", time: "2分钟前" },
      { id: "2", user: "用户002", agent: "产品咨询助手", messages: 8, status: "已结束", time: "5分钟前" },
      { id: "3", user: "用户003", agent: "投诉处理专员", messages: 15, status: "进行中", time: "8分钟前" }
    ],
    usage: {
      current: 8420,
      limit: 10000,
      percentage: 84.2
    }
  }
};

export default function ApplicationDetail() {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState("overview");
  
  const app = applicationData[id as keyof typeof applicationData];
  
  if (!app) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">应用不存在</h2>
          <p className="text-muted-foreground mb-4">未找到指定的应用信息</p>
          <Link to="/applications">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回应用列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/applications">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">{app.name}</h1>
            <p className="text-muted-foreground mt-1">{app.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge 
            variant={app.status === "运行中" ? "default" : "secondary"}
            className={app.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
          >
            {app.status}
          </Badge>
          <Link to={`/applications/${id}/edit`}>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Edit className="mr-2 h-4 w-4" />
              编辑应用
            </Button>
          </Link>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              智能体数量
            </CardTitle>
            <Bot className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{app.agentCount}</div>
            <p className="text-xs text-muted-foreground">
              活跃智能体
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              月度请求
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{app.monthlyRequests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12% 较上月
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              成功率
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{app.successRate}%</div>
            <p className="text-xs text-muted-foreground">
              响应成功率
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              响应时间
            </CardTitle>
            <Activity className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{app.avgResponseTime}</div>
            <p className="text-xs text-muted-foreground">
              平均响应时间
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 标签页导航 */}
      <div className="flex space-x-1">
        <Button
          variant={activeTab === "overview" ? "default" : "ghost"}
          onClick={() => setActiveTab("overview")}
          className="h-9"
        >
          概览
        </Button>
        <Button
          variant={activeTab === "details" ? "default" : "ghost"}
          onClick={() => setActiveTab("details")}
          className="h-9"
        >
          详细信息
        </Button>
        <Button
          variant={activeTab === "agents" ? "default" : "ghost"}
          onClick={() => setActiveTab("agents")}
          className="h-9"
        >
          智能体
        </Button>
        <Button
          variant={activeTab === "sessions" ? "default" : "ghost"}
          onClick={() => setActiveTab("sessions")}
          className="h-9"
        >
          会话记录
        </Button>
        <Button
          variant={activeTab === "analytics" ? "default" : "ghost"}
          onClick={() => setActiveTab("analytics")}
          className="h-9"
        >
          数据分析
        </Button>
      </div>

      {/* 标签页内容 */}
      {activeTab === "overview" && (
        <div className="grid gap-6 lg:grid-cols-2">
          {/* 基本信息 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">应用ID</Label>
                  <p className="text-foreground font-mono">{app.id}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">应用Key</Label>
                  <p className="text-foreground font-mono">{app.key}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">创建时间</Label>
                  <p className="text-foreground">{app.createdAt}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">最后活动</Label>
                  <p className="text-foreground">{app.lastActivity}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">总用户数</Label>
                  <p className="text-foreground">{app.totalUsers}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">智能体数量</Label>
                  <p className="text-foreground">{app.agentCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 使用量统计 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">本月使用量</CardTitle>
              <CardDescription>API请求使用情况</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">已使用</span>
                  <span className="text-sm font-medium">{app.usage.current.toLocaleString()} / {app.usage.limit.toLocaleString()}</span>
                </div>
                <Progress value={app.usage.percentage} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  已使用 {app.usage.percentage.toFixed(1)}% 的月度配额
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === "details" && (
        <div className="grid gap-6">
          {/* 应用设置 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">应用设置</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">自动回复</Label>
                  <p className="text-foreground">{app.settings.autoReply ? "已启用" : "已禁用"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">最大并发会话数</Label>
                  <p className="text-foreground">{app.settings.maxConcurrentSessions}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">会话超时时间</Label>
                  <p className="text-foreground">{app.settings.sessionTimeout} 分钟</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">日志记录</Label>
                  <p className="text-foreground">{app.settings.enableLogging ? "已启用" : "已禁用"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">数据分析</Label>
                  <p className="text-foreground">{app.settings.enableAnalytics ? "已启用" : "已禁用"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">频率限制</Label>
                  <p className="text-foreground">{app.settings.rateLimitEnabled ? `${app.settings.rateLimitRequests}次/${app.settings.rateLimitWindow}分钟` : "已禁用"}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 安全配置 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">安全配置</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">身份验证</Label>
                  <p className="text-foreground">{app.security.requireAuth ? "已启用" : "已禁用"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">CORS支持</Label>
                  <p className="text-foreground">{app.security.enableCors ? "已启用" : "已禁用"}</p>
                </div>
                <div className="col-span-2">
                  <Label className="text-sm text-muted-foreground">允许的域名</Label>
                  <div className="mt-1">
                    {app.security.allowedOrigins.map((origin, index) => (
                      <p key={index} className="text-foreground text-sm font-mono">{origin}</p>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 界面配置 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Palette className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">界面配置</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">应用图标</Label>
                  <p className="text-foreground">{app.ui.icon || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">退出URL</Label>
                  <p className="text-foreground">{app.ui.signOutUrl || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">主题</Label>
                  <p className="text-foreground">{app.ui.theme}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">AI头像</Label>
                  <p className="text-foreground">{app.ui.aiAvatar || "未设置"}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">用户头像</Label>
                  <p className="text-foreground">{app.ui.userAvatar || "未设置"}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 推荐提示词 */}
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-primary" />
                <CardTitle className="text-foreground">推荐提示词</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {app.starterPrompts.map((prompt, index) => (
                <Card key={prompt.id} className="bg-muted/50 border-border">
                  <CardContent className="pt-3 pb-3">
                    <div className="space-y-2">
                      <div>
                        <Label className="text-sm font-medium text-foreground">{prompt.title}</Label>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">{prompt.content}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === "agents" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">智能体列表</CardTitle>
            <CardDescription>该应用下的所有智能体</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow className="border-border">
                  <TableHead className="text-muted-foreground">智能体名称</TableHead>
                  <TableHead className="text-muted-foreground">状态</TableHead>
                  <TableHead className="text-muted-foreground">请求数</TableHead>
                  <TableHead className="text-muted-foreground text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {app.agents.map((agent) => (
                  <TableRow key={agent.id} className="border-border">
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                          <Bot className="w-4 h-4 text-primary-foreground" />
                        </div>
                        <span className="text-foreground">{agent.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={agent.status === "运行中" ? "default" : "secondary"}
                        className={agent.status === "运行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                      >
                        {agent.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-foreground">{agent.requests.toLocaleString()}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <Link to={`/agents/${agent.id}`}>
                        <Button variant="ghost" size="sm">
                          <Settings className="mr-2 h-4 w-4" />
                          配置
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {activeTab === "sessions" && (
        <Card className="bg-card border-border shadow-card">
          <CardHeader>
            <CardTitle className="text-foreground">最近会话</CardTitle>
            <CardDescription>该应用的最近对话记录</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow className="border-border">
                  <TableHead className="text-muted-foreground">用户</TableHead>
                  <TableHead className="text-muted-foreground">智能体</TableHead>
                  <TableHead className="text-muted-foreground">消息数</TableHead>
                  <TableHead className="text-muted-foreground">状态</TableHead>
                  <TableHead className="text-muted-foreground">时间</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {app.recentSessions.map((session) => (
                  <TableRow key={session.id} className="border-border">
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-gradient-primary rounded-full flex items-center justify-center">
                          <Users className="w-3 h-3 text-primary-foreground" />
                        </div>
                        <span className="text-foreground">{session.user}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-foreground">{session.agent}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-foreground">{session.messages} 条</span>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={session.status === "进行中" ? "default" : "secondary"}
                        className={session.status === "进行中" ? "bg-green-500/20 text-green-500 border-green-500/30" : ""}
                      >
                        {session.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-muted-foreground">{session.time}</span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {activeTab === "analytics" && (
        <div className="grid gap-6 lg:grid-cols-2">
          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">性能指标</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">成功率</span>
                  <span className="text-sm font-medium">{app.successRate}%</span>
                </div>
                <Progress value={app.successRate} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">响应速度</span>
                  <span className="text-sm font-medium">优秀</span>
                </div>
                <Progress value={85} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">用户满意度</span>
                  <span className="text-sm font-medium">92%</span>
                </div>
                <Progress value={92} className="h-2" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card border-border shadow-card">
            <CardHeader>
              <CardTitle className="text-foreground">使用趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">今日请求</span>
                  <span className="text-sm font-medium text-foreground">342</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">昨日请求</span>
                  <span className="text-sm font-medium text-foreground">298</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">本周平均</span>
                  <span className="text-sm font-medium text-foreground">315</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">增长率</span>
                  <span className="text-sm font-medium text-green-500">+14.8%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

function Label({ className, children }: { className?: string; children: React.ReactNode }) {
  return <div className={className}>{children}</div>;
}